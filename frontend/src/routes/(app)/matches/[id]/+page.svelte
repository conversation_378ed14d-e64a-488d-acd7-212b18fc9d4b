<script lang="ts">
	import * as Card from '$lib/components/ui/card';
	import * as Table from '$lib/components/ui/table';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import { Progress } from '$lib/components/ui/progress';
	import { Separator } from '$lib/components/ui/separator';
	import {
		ArrowLeft,
		Users,
		Trophy,
		Target,
		Timer,
		MapPin,
		Calendar,
		Download,
		Share,
		BarChart3,
		Activity,
		Crosshair,
		Zap
	} from 'lucide-svelte';
	import type { PageData } from './$types';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	// Use real match data from the server
	let match = data.match;

	let finalScore = $derived(() => {
		if (match.rounds.length === 0) return 'N/A';
		const lastRound = match.rounds[match.rounds.length - 1];
		if (!lastRound) return 'N/A';
		return `${lastRound.ct_score} - ${lastRound.t_score}`;
	});

	let matchWinner = $derived(() => {
		if (match.rounds.length === 0) return 'N/A';
		const lastRound = match.rounds[match.rounds.length - 1];
		if (!lastRound) return 'N/A';
		return lastRound.ct_score > lastRound.t_score ? 'CT' : 'T';
	});

	let ctPlayers = $derived(match.players.filter((p) => p.team === 'CT'));
	let tPlayers = $derived(match.players.filter((p) => p.team === 'T'));

	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('en-US', {
			weekday: 'long',
			year: 'numeric',
			month: 'long',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	function getKD(player: any) {
		return player.deaths > 0 ? (player.kills / player.deaths).toFixed(2) : player.kills.toFixed(2);
	}

	function getTotalKills(team: string) {
		return match.players.filter((p) => p.team === team).reduce((sum, p) => sum + p.kills, 0);
	}

	function getTotalDeaths(team: string) {
		return match.players.filter((p) => p.team === team).reduce((sum, p) => sum + p.deaths, 0);
	}

	function getTeamADR(team: string) {
		// Mock ADR calculation
		return Math.floor(Math.random() * 30) + 70;
	}
</script>

<svelte:head>
	<title>Match {match.id} - {match.map_name} - Brainless Stats</title>
</svelte:head>

<div class="container mx-auto space-y-6 p-6">
	<!-- Header -->
	<div class="flex items-center gap-4">
		<Button href="/matches" variant="outline" size="sm" class="gap-2">
			<ArrowLeft class="h-4 w-4" />
			Back to Matches
		</Button>
		<div class="flex-1">
			<h1 class="text-3xl font-bold tracking-tight">Match Analysis</h1>
			<p class="text-muted-foreground">{match.map_name} • {formatDate(match.created_at)}</p>
		</div>
		<div class="flex gap-2">
			<Button variant="outline" size="sm" class="gap-2">
				<Download class="h-4 w-4" />
				Export
			</Button>
			<Button variant="outline" size="sm" class="gap-2">
				<Share class="h-4 w-4" />
				Share
			</Button>
		</div>
	</div>

	<!-- Match Overview -->
	<div class="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
		<Card.Root>
			<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
				<Card.Title class="text-sm font-medium">Final Score</Card.Title>
				<Trophy class="h-4 w-4 text-muted-foreground" />
			</Card.Header>
			<Card.Content>
				<div class="text-2xl font-bold">{finalScore}</div>
				<p class="text-xs text-muted-foreground">
					Winner: <span class="font-medium">{matchWinner}</span>
				</p>
			</Card.Content>
		</Card.Root>

		<Card.Root>
			<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
				<Card.Title class="text-sm font-medium">Map</Card.Title>
				<MapPin class="h-4 w-4 text-muted-foreground" />
			</Card.Header>
			<Card.Content>
				<div class="text-2xl font-bold">{match.map_name}</div>
				<p class="text-xs text-muted-foreground">Active Duty</p>
			</Card.Content>
		</Card.Root>

		<Card.Root>
			<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
				<Card.Title class="text-sm font-medium">Rounds</Card.Title>
				<Activity class="h-4 w-4 text-muted-foreground" />
			</Card.Header>
			<Card.Content>
				<div class="text-2xl font-bold">{match.rounds.length}</div>
				<p class="text-xs text-muted-foreground">Total rounds played</p>
			</Card.Content>
		</Card.Root>

		<Card.Root>
			<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
				<Card.Title class="text-sm font-medium">Duration</Card.Title>
				<Timer class="h-4 w-4 text-muted-foreground" />
			</Card.Header>
			<Card.Content>
				<div class="text-2xl font-bold">42m</div>
				<p class="text-xs text-muted-foreground">Match duration</p>
			</Card.Content>
		</Card.Root>
	</div>

	<!-- Team Performance -->
	<div class="grid gap-6 lg:grid-cols-2">
		<!-- Counter-Terrorists -->
		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<div class="h-4 w-4 rounded bg-blue-500"></div>
					Counter-Terrorists
				</Card.Title>
				<div class="flex items-center gap-4 text-sm text-muted-foreground">
					<span>Kills: {getTotalKills('CT')}</span>
					<span>Deaths: {getTotalDeaths('CT')}</span>
					<span>ADR: {getTeamADR('CT')}</span>
				</div>
			</Card.Header>
			<Card.Content>
				<Table.Root>
					<Table.Header>
						<Table.Row>
							<Table.Head>Player</Table.Head>
							<Table.Head class="text-center">K</Table.Head>
							<Table.Head class="text-center">D</Table.Head>
							<Table.Head class="text-center">A</Table.Head>
							<Table.Head class="text-center">K/D</Table.Head>
						</Table.Row>
					</Table.Header>
					<Table.Body>
						{#each ctPlayers as player}
							<Table.Row>
								<Table.Cell class="font-medium">{player.name}</Table.Cell>
								<Table.Cell class="text-center">{player.kills}</Table.Cell>
								<Table.Cell class="text-center">{player.deaths}</Table.Cell>
								<Table.Cell class="text-center">{player.assists}</Table.Cell>
								<Table.Cell class="text-center font-mono">{getKD(player)}</Table.Cell>
							</Table.Row>
						{/each}
					</Table.Body>
				</Table.Root>
			</Card.Content>
		</Card.Root>

		<!-- Terrorists -->
		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<div class="h-4 w-4 rounded bg-orange-500"></div>
					Terrorists
				</Card.Title>
				<div class="flex items-center gap-4 text-sm text-muted-foreground">
					<span>Kills: {getTotalKills('T')}</span>
					<span>Deaths: {getTotalDeaths('T')}</span>
					<span>ADR: {getTeamADR('T')}</span>
				</div>
			</Card.Header>
			<Card.Content>
				<Table.Root>
					<Table.Header>
						<Table.Row>
							<Table.Head>Player</Table.Head>
							<Table.Head class="text-center">K</Table.Head>
							<Table.Head class="text-center">D</Table.Head>
							<Table.Head class="text-center">A</Table.Head>
							<Table.Head class="text-center">K/D</Table.Head>
						</Table.Row>
					</Table.Header>
					<Table.Body>
						{#each tPlayers as player}
							<Table.Row>
								<Table.Cell class="font-medium">{player.name}</Table.Cell>
								<Table.Cell class="text-center">{player.kills}</Table.Cell>
								<Table.Cell class="text-center">{player.deaths}</Table.Cell>
								<Table.Cell class="text-center">{player.assists}</Table.Cell>
								<Table.Cell class="text-center font-mono">{getKD(player)}</Table.Cell>
							</Table.Row>
						{/each}
					</Table.Body>
				</Table.Root>
			</Card.Content>
		</Card.Root>
	</div>

	<!-- Round History -->
	<Card.Root>
		<Card.Header>
			<Card.Title class="flex items-center gap-2">
				<BarChart3 class="h-5 w-5" />
				Round History
			</Card.Title>
			<Card.Description>Round-by-round progression and outcomes</Card.Description>
		</Card.Header>
		<Card.Content>
			<div class="space-y-4">
				<!-- Score Progress -->
				<div class="grid grid-cols-2 gap-4">
					<div>
						<div class="mb-2 flex items-center justify-between">
							<span class="flex items-center gap-2 text-sm font-medium">
								<div class="h-3 w-3 rounded bg-blue-500"></div>
								Counter-Terrorists
							</span>
							<span class="text-sm font-bold"
								>{match.rounds[match.rounds.length - 1]?.ct_score ?? 0}</span
							>
						</div>
						<Progress
							value={((match.rounds[match.rounds.length - 1]?.ct_score ?? 0) / 16) * 100}
							class="h-2"
						/>
					</div>
					<div>
						<div class="mb-2 flex items-center justify-between">
							<span class="flex items-center gap-2 text-sm font-medium">
								<div class="h-3 w-3 rounded bg-orange-500"></div>
								Terrorists
							</span>
							<span class="text-sm font-bold"
								>{match.rounds[match.rounds.length - 1]?.t_score ?? 0}</span
							>
						</div>
						<Progress
							value={((match.rounds[match.rounds.length - 1]?.t_score ?? 0) / 16) * 100}
							class="h-2"
						/>
					</div>
				</div>

				<!-- Round Visualization -->
				<div class="grid-cols-15 grid gap-1">
					{#each match.rounds.slice(0, 30) as round, i}
						<div
							class="flex aspect-square items-center justify-center rounded text-xs font-bold text-white {round.winner ===
							'CT'
								? 'bg-blue-500'
								: 'bg-orange-500'}"
							title="Round {round.round_number}: {round.winner} won by {round.win_reason}"
						>
							{round.round_number}
						</div>
					{/each}
				</div>

				<div class="flex items-center gap-4 text-xs text-muted-foreground">
					<div class="flex items-center gap-2">
						<div class="h-3 w-3 rounded bg-blue-500"></div>
						<span>CT Win</span>
					</div>
					<div class="flex items-center gap-2">
						<div class="h-3 w-3 rounded bg-orange-500"></div>
						<span>T Win</span>
					</div>
				</div>
			</div>
		</Card.Content>
	</Card.Root>

	<!-- Match Statistics -->
	<div class="grid gap-6 lg:grid-cols-3">
		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Target class="h-5 w-5" />
					Top Fragger
				</Card.Title>
			</Card.Header>
			<Card.Content>
				{@const topPlayer = match.players.reduce((max, player) =>
					player.kills > max.kills ? player : max
				)}
				<div class="flex items-center gap-3">
					<div
						class="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-yellow-400 to-orange-500 font-bold text-white"
					>
						{topPlayer.name.slice(0, 2).toUpperCase()}
					</div>
					<div>
						<div class="font-medium">{topPlayer.name}</div>
						<div class="text-sm text-muted-foreground">{topPlayer.kills} kills</div>
					</div>
					<Badge variant="default" class="ml-auto">MVP</Badge>
				</div>
			</Card.Content>
		</Card.Root>

		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Crosshair class="h-5 w-5" />
					Total Kills
				</Card.Title>
			</Card.Header>
			<Card.Content>
				<div class="text-2xl font-bold">
					{match.players.reduce((sum, p) => sum + p.kills, 0)}
				</div>
				<p class="text-xs text-muted-foreground">Across all players</p>
			</Card.Content>
		</Card.Root>

		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Zap class="h-5 w-5" />
					Average K/D
				</Card.Title>
			</Card.Header>
			<Card.Content>
				<div class="text-2xl font-bold">
					{(
						match.players.reduce(
							(sum, p) => sum + (p.deaths > 0 ? p.kills / p.deaths : p.kills),
							0
						) / match.players.length
					).toFixed(2)}
				</div>
				<p class="text-xs text-muted-foreground">Team average</p>
			</Card.Content>
		</Card.Root>
	</div>
</div>

<style>
	.grid-cols-15 {
		grid-template-columns: repeat(15, minmax(0, 1fr));
	}
</style>
