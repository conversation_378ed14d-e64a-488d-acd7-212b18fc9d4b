# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Enhanced demo upload functionality with improved file handling
- Drag and drop support for demo file uploads
- Click-to-browse functionality for file selection
- Improved form validation for demo uploads
- Better error handling and user feedback for uploads
- Enhanced Steam authentication flow
- Improved UI components with consistent styling
- Better mobile responsiveness with updated hooks

### Changed
- **Backend**: Refactored demo upload API endpoint parameters for better usability
- **Frontend**: Improved demo upload form with better UX and validation
- **Authentication**: Streamlined Steam OpenID authentication process
- **UI/UX**: Updated styling and component layouts for better consistency
- **Code Quality**: Applied consistent code formatting across all files
- **Testing**: Enhanced test coverage and reliability for demo upload flows
- **Configuration**: Updated TypeScript and build configurations

### Fixed
- Demo file upload form validation issues
- Steam authentication callback handling
- Mobile UI responsiveness issues
- Test reliability and flakiness
- Code formatting inconsistencies

### Technical Improvements
- Migrated from shell scripts to native pnpm and Python CLI tools
- Enhanced Docker configuration with proper health checks
- Improved development workflow with unified task runners
- Updated all README files with current architecture
- Comprehensive monorepo setup with pnpm workspaces
- Python CLI tool with Typer for backend operations
- Node.js task runner for frontend operations
- Universal task runner for cross-platform compatibility
- Multi-stage Docker builds for development and production
- Nginx reverse proxy configuration with SSL support
- GitHub Actions CI/CD pipeline
- Health check endpoints for all services
- Comprehensive documentation and guides

### Removed
- Shell scripts in favor of native tooling
- Manual dependency management in favor of package managers

## [0.1.0] - 2024-01-XX

### Added
- Initial project setup
- SvelteKit frontend with TypeScript
- FastAPI backend with Python
- FastStream demo processing microservice
- Basic Docker configuration
- Redis integration for message queuing
- CS2 demo parsing capabilities
- Basic UI components with Shadcn/ui

### Infrastructure
- UV workspace for Python monorepo
- pnpm workspace for frontend
- Docker Compose for development
- Basic CI/CD pipeline

### Documentation
- Project README
- Basic setup instructions
- API documentation via FastAPI

---

## Release Notes Format

### Types of Changes
- **Added** for new features
- **Changed** for changes in existing functionality
- **Deprecated** for soon-to-be removed features
- **Removed** for now removed features
- **Fixed** for any bug fixes
- **Security** for vulnerability fixes

### Categories
- **Frontend** - SvelteKit application changes
- **Backend** - FastAPI application changes
- **Demo Processing** - Microservice changes
- **Infrastructure** - Docker, CI/CD, deployment changes
- **Documentation** - Documentation updates
- **Dependencies** - Dependency updates
