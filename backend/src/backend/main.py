from contextlib import asynccontextmanager
from typing import Annotated

import httpx
from fastapi import (
    APIRouter,
    Depends,
    FastAP<PERSON>,
    File,
    HTTPException,
    Query,
    Response,
    UploadFile,
    status,
)
from fastapi.background import BackgroundTasks
from sqlalchemy import Connection

from backend import crud, demo_storage, schemas
from backend.clients.demo_processing_client import DemoProcessingClient
from backend.clients.steam_api import MatchInfo, SteamAPI, get_steam_api
from backend.database import get_connection_dependency, init_db
from backend.error_handling import Message
from backend.logging_config import configure_logging, get_logger

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(_app: FastAPI):  # noqa: RUF029
    """Initialize the application on startup.

    This function is called when the application starts up.
    It initializes the database and performs any other necessary setup.
    """
    # Configure logging
    configure_logging("INFO")
    logger.info("Starting backend service")

    # This is not truly async, but FastAPI expects an async context manager
    init_db()
    yield

    # Shutdown
    logger.info("Shutting down backend service")
    await cleanup_demo_processing_client()


app = FastAPI(lifespan=lifespan)

api_router = APIRouter()


# Global demo processing client instance
_demo_processing_client: DemoProcessingClient | None = None


def get_demo_processing_client() -> DemoProcessingClient:
    """Get demo processing client instance (singleton).

    Returns:
        DemoProcessingClient instance
    """
    global _demo_processing_client
    if _demo_processing_client is None:
        _demo_processing_client = DemoProcessingClient()
    return _demo_processing_client


async def cleanup_demo_processing_client():
    """Cleanup the demo processing client."""
    global _demo_processing_client
    if _demo_processing_client is not None:
        await _demo_processing_client.close()
        _demo_processing_client = None


@api_router.get("/users/steamid/{steam_id}")
async def read_user_by_steam_id(
    steam_id: str, conn: Annotated[Connection, Depends(get_connection_dependency)]
) -> schemas.UserRead | None:
    """Get a user by their Steam ID.

    Args:
        steam_id: The Steam ID of the user to retrieve
        conn: Database connection

    Returns:
        The user with the specified Steam ID
    """
    return crud.read_user_by_steam_id(steam_id, conn)


@api_router.post("/users/register")
async def register_user(
    user: schemas.UserCreate,
    conn: Annotated[Connection, Depends(get_connection_dependency)],
    steam_api_instance: Annotated[SteamAPI, Depends(dependency=get_steam_api)],
    response: Response,
) -> dict | None:
    """Register a new user.

    Args:
        user: User data to create
        conn: Database connection
        steam_api_instance: Steam API client
        response: FastAPI response object

    Returns:
        The created user or None if the user already exists
    """
    created_user = await crud.create_user(user, conn, steam_api_instance)
    if created_user is None:
        response.status_code = status.HTTP_409_CONFLICT
        return None
    return created_user


@api_router.get("/users/steamid/{steam_id}/registered")
async def check_user_registered(
    steam_id: str, conn: Annotated[Connection, Depends(get_connection_dependency)]
) -> bool:
    """Check if a user is registered.

    Args:
        steam_id: The Steam ID to check
        conn: Database connection

    Returns:
        True if the user is registered, False otherwise
    """
    return crud.is_user_registered(steam_id, conn)


@api_router.get(
    "/users/{user_id}",
    response_model=schemas.UserReadWithTracking,
    responses={404: {"model": Message}},
)
async def read_user(
    user_id: int, conn: Annotated[Connection, Depends(get_connection_dependency)]
) -> schemas.UserReadWithTracking:
    """Get a user by their ID.

    Args:
        user_id: The ID of the user to retrieve
        conn: Database connection

    Returns:
        The user with the specified ID

    """
    return crud.read_user(user_id, conn)


@api_router.get(
    "/users/{user_id}/tracking",
    response_model=schemas.TrackingDetailsRead,
    responses={404: {"model": Message}},
)
async def read_user_tracking(
    user_id: int, conn: Annotated[Connection, Depends(get_connection_dependency)]
) -> schemas.TrackingDetailsRead:
    """Get tracking details for a user.

    Args:
        user_id: The ID of the user to get tracking details for
        conn: Database connection

    Returns:
        The tracking details for the user

    """
    return crud.read_tracking_details(user_id, conn)


@api_router.post(
    "/users/{user_id}/tracking",
    response_model=schemas.TrackingDetailsRead,
    status_code=status.HTTP_201_CREATED,
    responses={
        404: {"model": Message},  # User not found
        409: {"model": Message},  # Tracking already exists
    },
)
async def create_user_tracking(
    user_id: int,
    tracking: schemas.TrackingDetailsCreate,
    conn: Annotated[Connection, Depends(get_connection_dependency)],
) -> schemas.TrackingDetailsRead:
    """Create tracking details for a user.

    Args:
        user_id: The ID of the user to create tracking details for
        tracking: The tracking details to create
        conn: Database connection

    Returns:
        The created tracking details

    """
    return crud.create_tracking_details(user_id, tracking, conn)


async def download_and_save_demo(
    match_id: str,
    demo_url: str,
    demo_storage_instance: demo_storage.DemoStorage,
    conn: Connection,
) -> None:
    """Download and save a demo file from the given URL."""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(demo_url)
            _ = response.raise_for_status()
            data = response.content  # This will be bytes

            _ = await demo_storage_instance.save_demo_file(match_id, data)
    except (httpx.HTTPError, demo_storage.DemoStorageError) as e:
        # Update demo file status to failed
        demo = crud.read_demo_file_by_match(match_id, conn)
        _ = crud.update_demo_file(
            demo.id,
            {"status": schemas.DemoFileStatus.FAILED, "error_message": str(e)},
            conn,
        )


# Demo management endpoints


@api_router.post(
    "/matches/{match_id}/demo",
    response_model=schemas.DemoFileRead,
    responses={404: {"model": Message}},
)
async def request_demo_download(
    match_id: str,
    background_tasks: BackgroundTasks,
    conn: Annotated[Connection, Depends(get_connection_dependency)],
    steam_api: Annotated[SteamAPI, Depends(dependency=get_steam_api)],
) -> schemas.DemoFileRead:
    """Request a demo file download for a match.

    This endpoint will:
    1. Get match info from Steam
    2. Create a demo file record
    3. Queue the download in background

    Args:
        match_id: The match ID to download the demo for
        background_tasks: FastAPI background tasks
        conn: Database connection
        steam_api: Steam API client

    Returns:
        The created demo file record

    """
    # First check if we already have this demo
    try:
        return crud.read_demo_file_by_match(match_id, conn)
    except HTTPException:
        # Demo not found, continue with download
        pass

    # Get match info from Steam
    match_info = await steam_api.get_match_info(match_id)

    # Create the demo file record
    demo_storage_instance = demo_storage.get_demo_storage()
    file_path = demo_storage_instance.generate_file_path(match_id)

    demo = schemas.DemoFileCreate(
        match_id=match_id,
        file_path=str(file_path),
    )
    demo_file = crud.create_demo_file(demo, conn)

    # Queue download in background
    background_tasks.add_task(
        download_and_save_demo,
        match_id,
        match_info.demo_url,
        demo_storage_instance,
        conn,
    )

    return demo_file


@api_router.get(
    "/matches/{match_id}/demo/status",
    response_model=schemas.DemoFileRead,
    responses={404: {"model": Message}},
)
async def get_demo_status(
    match_id: str,
    conn: Annotated[Connection, Depends(get_connection_dependency)],
) -> schemas.DemoFileRead:
    """Get the current status of a demo file."""
    return crud.read_demo_file_by_match(match_id, conn)


@api_router.post(
    "/demos/upload",
    response_model=schemas.DemoFileRead,
    status_code=status.HTTP_201_CREATED,
)
async def upload_demo_file(
    background_tasks: BackgroundTasks,
    conn: Annotated[Connection, Depends(get_connection_dependency)],
    demo_processing_client: Annotated[DemoProcessingClient, Depends(get_demo_processing_client)],
    match_id: str = Query(..., description="The match ID to associate with the demo file"),
    demo_file: UploadFile = File(..., description="The demo file to upload"),
) -> schemas.DemoFileRead:
    """Upload a demo file manually.

    This endpoint will:
    1. Save the uploaded demo file
    2. Create a demo file record

    Args:
        match_id: The match ID to associate with the demo file
        demo_file: The uploaded demo file
        conn: Database connection

    Returns:
        The created demo file record

    Raises:
        HTTPException: If the file is not a valid demo file or there's an error saving it
    """
    # Check if file is a valid demo file (has .dem extension)
    if not demo_file.filename or not demo_file.filename.endswith(".dem"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File must be a .dem file",
        )

    # Read file content
    content = await demo_file.read()

    # Save the demo file
    demo_storage_instance = demo_storage.get_demo_storage()
    file_path = await demo_storage_instance.save_demo_file(match_id, content)

    # Create the demo file record
    demo = schemas.DemoFileCreate(
        match_id=match_id,
        file_path=str(file_path),
        status=schemas.DemoFileStatus.DOWNLOADED,  # Mark as already downloaded
    )
    created_demo = crud.create_demo_file(demo, conn)

    # Trigger automatic processing in background
    background_tasks.add_task(
        parse_demo_file,
        created_demo.id,
        demo_processing_client,
        conn,
    )

    return created_demo


@api_router.get(
    "/demos",
    response_model=list[schemas.DemoFileRead],
)
async def list_demo_files(
    conn: Annotated[Connection, Depends(get_connection_dependency)],
) -> list[schemas.DemoFileRead]:
    """List all demo files."""
    return crud.list_demo_files(conn)


@api_router.get(
    "/matches/{match_id}/info",
    responses={404: {"model": Message}},
)
async def get_match_info(
    match_id: str,
    steam_api_instance: Annotated[SteamAPI, Depends(get_steam_api)],
) -> MatchInfo:
    """Get information about a match from Steam."""
    return await steam_api_instance.get_match_info(match_id)


# Demo parsing endpoints


async def parse_demo_file(
    demo_id: int,
    demo_processing_client: DemoProcessingClient,
    conn: Connection,
) -> None:
    """Parse a demo file and store the results in the database.

    Args:
        demo_id: ID of the demo file to parse
        demo_processing_client: Demo processing microservice client
        conn: Database connection
    """
    try:
        # Get the demo file
        demo = crud.read_demo_file(demo_id, conn)
        if not demo:
            return

        # Update status to processing
        if demo.id is not None:
            _ = crud.update_demo_file(
                demo.id,
                {"status": schemas.DemoFileStatus.PROCESSING},
                conn,
            )

        # Request demo processing from microservice
        result = await demo_processing_client.request_demo_processing(
            demo_file_path=demo.file_path,
            timeout_seconds=300,
        )

        if result.success and result.demo_data:
            # Convert demo data to match data format expected by crud.create_match
            map_info = result.demo_data.get("map_info", {})
            players_data = result.demo_data.get("players", [])
            rounds_data = result.demo_data.get("rounds", [])
            
            match_data = {
                "map": {"name": map_info.get("name", "unknown")},
                "players": [
                    {
                        "steam_id": player.get("steam_id", ""),
                        "name": player.get("name", ""),
                        "team": player.get("team", "UNKNOWN"),
                    }
                    for player in players_data
                ],
                "rounds": [
                    {
                        "round_number": round_data.get("round_number", 0),
                        "winner": round_data.get("winner", "UNKNOWN"),
                        "win_reason": round_data.get("win_reason", "UNKNOWN"),
                        "ct_score": round_data.get("ct_score", 0),
                        "t_score": round_data.get("t_score", 0),
                    }
                    for round_data in rounds_data
                ],
                "match_info": result.demo_data.get("match_info", {}),
            }

            # Create match record with the parsed data
            if demo.id is not None:
                _ = crud.create_match(match_data, demo.id, conn)

            # Update demo file status to processed
            if demo.id is not None:
                _ = crud.update_demo_file(
                    demo.id,
                    {"status": schemas.DemoFileStatus.PROCESSED},
                    conn,
                )
        else:
            # Processing failed
            _ = crud.update_demo_file(
                demo_id,
                {
                    "status": schemas.DemoFileStatus.FAILED,
                    "error_message": result.error_message or "Unknown processing error",
                },
                conn,
            )

    except Exception as e:
        # Update demo file status to failed
        _ = crud.update_demo_file(
            demo_id,
            {
                "status": schemas.DemoFileStatus.FAILED,
                "error_message": str(e),
            },
            conn,
        )


@api_router.post(
    "/matches/{match_id}/demo/parse",
    response_model=schemas.DemoFileRead,
    responses={404: {"model": Message}},
)
async def request_demo_parsing(
    match_id: str,
    background_tasks: BackgroundTasks,
    conn: Annotated[Connection, Depends(get_connection_dependency)],
    demo_processing_client: Annotated[DemoProcessingClient, Depends(get_demo_processing_client)],
) -> schemas.DemoFileRead:
    """Request parsing of a demo file.

    This endpoint will:
    1. Check if the demo file exists and is downloaded
    2. Queue the parsing in background using the demo processing microservice

    Args:
        match_id: The match ID to parse the demo for
        background_tasks: FastAPI background tasks
        conn: Database connection
        demo_processing_client: Demo processing microservice client

    Returns:
        The updated demo file record

    Raises:
        HTTPException: If demo file is not found or not downloaded
    """
    # Get the demo file
    demo = crud.read_demo_file_by_match(match_id, conn)

    # Check if the demo file is downloaded
    if demo.status != schemas.DemoFileStatus.DOWNLOADED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Demo file is not downloaded",
        )

    # Queue parsing in background
    background_tasks.add_task(
        parse_demo_file,
        demo.id,
        demo_processing_client,
        conn,
    )

    return demo


@api_router.get(
    "/matches",
    response_model=list[schemas.MatchRead],
)
async def list_matches(
    conn: Annotated[Connection, Depends(get_connection_dependency)],
) -> list[schemas.MatchRead]:
    """List all matches."""
    return crud.list_matches(conn)


@api_router.get(
    "/matches/{match_id}",
    response_model=schemas.MatchRead,
    responses={404: {"model": Message}},
)
async def get_match(
    match_id: int,
    conn: Annotated[Connection, Depends(get_connection_dependency)],
) -> schemas.MatchRead:
    """Get a match by ID."""
    return crud.read_match(match_id, conn)


@api_router.get(
    "/demos/{demo_id}/match",
    response_model=schemas.MatchRead,
    responses={404: {"model": Message}},
)
async def get_match_by_demo(
    demo_id: int,
    conn: Annotated[Connection, Depends(get_connection_dependency)],
) -> schemas.MatchRead:
    """Get match data for a demo file."""
    return crud.read_match_by_demo_file(demo_id, conn)


@app.get("/health")
async def health_check() -> dict[str, str]:
    """Health check endpoint for monitoring and load balancers.

    Returns:
        Health status information
    """
    return {
        "status": "healthy",
        "service": "backend",
        "version": "0.1.0",
    }


app.include_router(api_router, prefix="/api")

# Cleanup task running in background
background_tasks = BackgroundTasks()
