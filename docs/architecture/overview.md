# System Architecture Overview

This document provides a comprehensive overview of Brainless Stats' system architecture, design patterns, and component interactions.

## 🏗️ High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        Browser[Web Browser]
        Mobile[Mobile Browser]
    end
    
    subgraph "Presentation Layer"
        Frontend[SvelteKit Frontend<br/>Port 3000]
        Nginx[Nginx Reverse Proxy<br/>Port 80/443]
    end
    
    subgraph "Application Layer"
        API[FastAPI Backend<br/>Port 8000]
        Auth[Steam OAuth]
        WebSocket[WebSocket Connections]
    end
    
    subgraph "Processing Layer"
        DemoProc[Demo Processing Service<br/>FastStream]
        Queue[Redis Message Queue<br/>Port 6379]
        Background[Background Tasks]
    end
    
    subgraph "Data Layer"
        SQLite[(SQLite Database)]
        FileSystem[(Demo File Storage)]
        Cache[(Redis Cache)]
    end
    
    subgraph "External Services"
        Steam[Steam Web API]
        SteamAuth[Steam OpenID]
    end
    
    Browser --> Nginx
    Mobile --> Nginx
    Nginx --> Frontend
    Frontend --> API
    API --> Auth
    Auth --> SteamAuth
    API --> Queue
    Queue --> DemoProc
    DemoProc --> Steam
    API --> SQLite
    DemoProc --> SQLite
    API --> FileSystem
    DemoProc --> FileSystem
    API --> Cache
    Queue --> Cache
```

## 🎯 Design Principles

### 1. Microservice Architecture
- **Separation of Concerns**: Each service has a single responsibility
- **Loose Coupling**: Services communicate through well-defined APIs
- **Independent Deployment**: Services can be deployed independently
- **Technology Diversity**: Best tool for each job

### 2. Event-Driven Processing
- **Asynchronous Operations**: Long-running tasks don't block user interactions
- **Message Queues**: Redis-based message passing between services
- **Event Sourcing**: Important events are captured and stored
- **Real-time Updates**: WebSocket connections for live status updates

### 3. Scalability & Performance
- **Horizontal Scaling**: Services can be scaled independently
- **Caching Strategy**: Multiple caching layers for performance
- **Efficient Data Processing**: Optimized demo parsing algorithms
- **Resource Management**: Proper resource allocation and limits

### 4. Developer Experience
- **Hot Reloading**: Instant feedback during development
- **Type Safety**: TypeScript throughout the stack
- **API Documentation**: Auto-generated OpenAPI specs
- **Testing**: Comprehensive test coverage

## 🏢 Service Architecture

### Frontend Service (SvelteKit)
```mermaid
graph LR
    subgraph "Frontend Architecture"
        Router[SvelteKit Router]
        Components[Svelte Components]
        Stores[Svelte Stores]
        API[API Client]
        Auth[Auth Module]
        
        Router --> Components
        Components --> Stores
        Components --> API
        API --> Auth
        Stores --> Components
    end
```

**Responsibilities:**
- User interface and experience
- Client-side routing and navigation
- State management with Svelte stores
- API communication with backend
- User authentication flow
- Real-time updates via WebSockets

**Technology Stack:**
- **Framework**: SvelteKit 2.x
- **Language**: TypeScript
- **Styling**: Tailwind CSS + shadcn/ui
- **Build Tool**: Vite
- **Package Manager**: pnpm

### Backend Service (FastAPI)
```mermaid
graph LR
    subgraph "Backend Architecture"
        Router[API Router]
        Auth[Authentication]
        CRUD[CRUD Operations]
        Models[Database Models]
        Schemas[Pydantic Schemas]
        Tasks[Background Tasks]
        
        Router --> Auth
        Router --> CRUD
        CRUD --> Models
        Router --> Schemas
        Router --> Tasks
        Models --> Schemas
    end
```

**Responsibilities:**
- RESTful API endpoints
- User authentication and authorization
- Database operations and management
- File upload and management
- Background task coordination
- Steam API integration

**Technology Stack:**
- **Framework**: FastAPI 0.104+
- **Language**: Python 3.13
- **Database**: SQLAlchemy with SQLite
- **Validation**: Pydantic
- **Task Queue**: Redis + Background Tasks
- **Documentation**: OpenAPI/Swagger

### Demo Processing Service (FastStream)
```mermaid
graph LR
    subgraph "Demo Processing Architecture"
        Consumer[Message Consumer]
        Parser[Demo Parser]
        Analyzer[Data Analyzer]
        Storage[Data Storage]
        Publisher[Event Publisher]
        
        Consumer --> Parser
        Parser --> Analyzer
        Analyzer --> Storage
        Storage --> Publisher
    end
```

**Responsibilities:**
- CS2 demo file parsing
- Game event extraction and analysis
- Statistical computation
- Match data generation
- Performance metrics calculation
- Progress reporting

**Technology Stack:**
- **Framework**: FastStream (async messaging)
- **Message Broker**: Redis
- **Demo Parsing**: demoparser2, awpy
- **Data Processing**: pandas, numpy
- **Format Support**: CS2 .dem files

## 🔄 Data Flow Architecture

### 1. User Registration & Authentication
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend
    participant S as Steam API
    
    U->>F: Click "Login with Steam"
    F->>B: Initiate Steam OAuth
    B->>S: Redirect to Steam OpenID
    S->>U: Steam login form
    U->>S: Enter credentials
    S->>B: Return user data
    B->>F: Create session + JWT token
    F->>U: Redirect to dashboard
```

### 2. Demo Upload & Processing
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend
    participant Q as Redis Queue
    participant P as Demo Processor
    participant DB as Database
    
    U->>F: Upload demo file
    F->>B: POST /api/demos/upload
    B->>DB: Create demo record
    B->>Q: Queue processing job
    B->>F: Return upload success
    Q->>P: Consume processing job
    P->>P: Parse demo file
    P->>DB: Store match data
    P->>Q: Publish completion event
    Q->>F: WebSocket notification
    F->>U: Show processing complete
```

### 3. Match Analysis Request
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend
    participant C as Cache
    participant DB as Database
    
    U->>F: Request match analysis
    F->>B: GET /api/matches/{id}
    B->>C: Check cache
    alt Cache hit
        C->>B: Return cached data
    else Cache miss
        B->>DB: Query match data
        DB->>B: Return match data
        B->>C: Store in cache
    end
    B->>F: Return analysis data
    F->>U: Display match analysis
```

## 🗄️ Data Architecture

### Database Schema Overview
```mermaid
erDiagram
    User {
        int id PK
        string steam_id UK
        string username
        string profile_picture_url
        datetime created_at
    }
    
    TrackingDetails {
        int id PK
        int user_id FK
        string auth_code
        string match_token
        datetime created_at
    }
    
    DemoFile {
        int id PK
        string match_id
        string file_path
        enum status
        string error_message
        datetime created_at
    }
    
    Match {
        int id PK
        int demo_file_id FK
        string map_name
        datetime match_date
        json team_scores
        json player_stats
        datetime created_at
    }
    
    User ||--o| TrackingDetails : has
    DemoFile ||--o| Match : generates
```

### File Storage Strategy
```
storage/
├── demos/                    # Original demo files
│   ├── 2024/
│   │   ├── 01/
│   │   │   └── match_123_20240115.dem
│   │   └── 02/
│   └── 2023/
├── processed/               # Processed data cache
│   ├── json/               # Extracted JSON data
│   └── stats/              # Computed statistics
└── backups/                # Database backups
    ├── daily/
    └── weekly/
```

## 🔐 Security Architecture

### Authentication Flow
```mermaid
graph TB
    subgraph "Authentication Layer"
        SteamOAuth[Steam OpenID]
        JWTAuth[JWT Authentication]
        SessionMgmt[Session Management]
    end
    
    subgraph "Authorization Layer"
        RBAC[Role-Based Access]
        ResourceAuth[Resource Authorization]
        RateLimit[Rate Limiting]
    end
    
    subgraph "Security Measures"
        CORS[CORS Protection]
        HTTPS[HTTPS/TLS]
        Validation[Input Validation]
        Sanitization[Data Sanitization]
    end
    
    SteamOAuth --> JWTAuth
    JWTAuth --> SessionMgmt
    SessionMgmt --> RBAC
    RBAC --> ResourceAuth
    ResourceAuth --> RateLimit
```

### Security Layers
1. **Transport Security**: HTTPS/TLS encryption
2. **Authentication**: Steam OpenID integration
3. **Authorization**: JWT-based access control
4. **Input Validation**: Pydantic schema validation
5. **Rate Limiting**: Request rate limiting
6. **CORS Protection**: Cross-origin request filtering
7. **File Security**: Safe file upload handling

## 📊 Monitoring & Observability

### Application Metrics
```mermaid
graph LR
    subgraph "Metrics Collection"
        AppMetrics[Application Metrics]
        SystemMetrics[System Metrics]
        BusinessMetrics[Business Metrics]
    end
    
    subgraph "Processing"
        Aggregation[Metric Aggregation]
        Analysis[Trend Analysis]
        Alerting[Alert Generation]
    end
    
    subgraph "Visualization"
        Dashboards[Monitoring Dashboards]
        Reports[Performance Reports]
        Logs[Structured Logs]
    end
    
    AppMetrics --> Aggregation
    SystemMetrics --> Aggregation
    BusinessMetrics --> Aggregation
    Aggregation --> Analysis
    Analysis --> Alerting
    Analysis --> Dashboards
    Dashboards --> Reports
    Alerting --> Logs
```

### Health Check Strategy
- **Application Health**: `/health` endpoints for each service
- **Dependency Health**: Database, Redis, external API connectivity
- **Resource Health**: Memory, CPU, disk usage monitoring
- **Business Health**: Demo processing rates, error rates

## 🚀 Deployment Architecture

### Development Environment
```mermaid
graph TB
    subgraph "Local Development"
        DevFrontend[Frontend Dev Server]
        DevBackend[Backend Dev Server]
        DevRedis[Redis Container]
        DevDB[(SQLite Files)]
    end
    
    DevFrontend --> DevBackend
    DevBackend --> DevRedis
    DevBackend --> DevDB
```

### Production Environment
```mermaid
graph TB
    subgraph "Production Infrastructure"
        LoadBalancer[Load Balancer]
        FrontendNodes[Frontend Containers]
        BackendNodes[Backend Containers]
        ProcessorNodes[Processor Containers]
        RedisCluster[Redis Cluster]
        Database[(Production Database)]
        FileStorage[(File Storage)]
    end
    
    LoadBalancer --> FrontendNodes
    LoadBalancer --> BackendNodes
    BackendNodes --> ProcessorNodes
    BackendNodes --> RedisCluster
    ProcessorNodes --> RedisCluster
    BackendNodes --> Database
    ProcessorNodes --> Database
    BackendNodes --> FileStorage
    ProcessorNodes --> FileStorage
```

## 🔧 Configuration Management

### Environment-based Configuration
- **Development**: Local files, minimal security
- **Staging**: Production-like, test data
- **Production**: Secure configuration, monitoring enabled

### Configuration Sources
1. **Environment Variables**: Runtime configuration
2. **Configuration Files**: Application defaults
3. **External Services**: Feature flags, secrets management
4. **Command Line**: Override specific settings

## 📈 Performance Considerations

### Optimization Strategies
1. **Caching**: Multi-level caching strategy
2. **Database Optimization**: Proper indexing, query optimization
3. **Asset Optimization**: Code splitting, compression
4. **CDN Integration**: Static asset delivery
5. **Background Processing**: Async processing for heavy operations

### Scalability Patterns
1. **Horizontal Scaling**: Multiple service instances
2. **Load Balancing**: Request distribution
3. **Database Sharding**: Data partitioning strategies
4. **Microservice Decomposition**: Service separation

## 🔄 Integration Patterns

### API Integration
- **RESTful APIs**: Standard HTTP-based communication
- **WebSocket**: Real-time bidirectional communication
- **Message Queues**: Asynchronous service communication
- **Event Streaming**: Event-driven architecture

### External Integrations
- **Steam Web API**: Game data and user information
- **Steam OpenID**: User authentication
- **File Storage**: Local and cloud storage options

## Next Steps

- **[Component Architecture](components.md)** - Detailed component interactions
- **[Database Design](database.md)** - Database schema and relationships
- **[API Design](api-design.md)** - REST API patterns and conventions
- **[Security Architecture](security.md)** - Security measures and practices

---

**This architecture provides a solid foundation for scalable, maintainable, and secure CS2 demo analysis! 🏗️**