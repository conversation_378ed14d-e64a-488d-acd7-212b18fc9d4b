# Configuration Guide

Comprehensive guide to configuring Brainless Stats for different environments and use cases.

## 🔧 Environment Configuration

### Development Environment

**Environment Files:**
```bash
# Root directory
.env

# Backend specific
backend/.env

# Frontend specific  
frontend/.env
```

**Root Configuration (.env):**
```bash
# Environment
ENV=development
COMPOSE_PROJECT_NAME=brainless-stats

# Optional: SSL Configuration (not needed for development)
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem
```

**Backend Configuration (backend/.env):**
```bash
# Environment
ENV=development
DEBUG=true

# Database
DATABASE_URL=sqlite:///./database.db

# Redis
REDIS_URL=redis://localhost:6379

# Steam API
STEAM_API_KEY=your_steam_api_key_here

# Security
JWT_SECRET=your-development-jwt-secret
CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173"]

# File Storage
DEMO_STORAGE_PATH=./storage/demos
MAX_UPLOAD_SIZE=104857600  # 100MB

# Logging
LOG_LEVEL=DEBUG
LOG_FORMAT=colored

# Demo Processing
DEMO_PROCESSING_TIMEOUT=300  # 5 minutes
DEMO_PROCESSING_CONCURRENCY=2
```

**Frontend Configuration (frontend/.env):**
```bash
# API Configuration
PUBLIC_API_BASE_URL=http://localhost:8000
PUBLIC_ENVIRONMENT=development

# Steam Integration
STEAM_API_KEY=your_steam_api_key_here

# Optional: Analytics (disable in development)
# PUBLIC_ANALYTICS_ID=
```

### Production Environment

**Root Configuration (.env):**
```bash
# Environment
ENV=production
COMPOSE_PROJECT_NAME=brainless-stats-prod

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/brainless-stats.crt
SSL_KEY_PATH=/etc/ssl/private/brainless-stats.key

# Domain
DOMAIN=yourdomain.com
```

**Backend Configuration (backend/.env):**
```bash
# Environment
ENV=production
DEBUG=false

# Database (consider PostgreSQL for production)
DATABASE_URL=sqlite:///./data/database.db
# DATABASE_URL=postgresql://user:password@localhost/brainless_stats

# Redis
REDIS_URL=redis://redis:6379
# REDIS_URL=redis://user:password@redis-host:6379

# Steam API
STEAM_API_KEY=your_production_steam_api_key

# Security (use strong, unique values)
JWT_SECRET=your-super-secure-production-jwt-secret-min-32-chars
SECRET_KEY=your-super-secure-production-secret-key

# CORS (restrict to your domain)
CORS_ORIGINS=["https://yourdomain.com"]

# File Storage
DEMO_STORAGE_PATH=/app/data/demos
MAX_UPLOAD_SIZE=209715200  # 200MB for production

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Demo Processing
DEMO_PROCESSING_TIMEOUT=600  # 10 minutes
DEMO_PROCESSING_CONCURRENCY=4

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60  # seconds

# Health Checks
HEALTH_CHECK_INTERVAL=30
```

**Frontend Configuration (frontend/.env):**
```bash
# API Configuration
PUBLIC_API_BASE_URL=https://yourdomain.com/api
PUBLIC_ENVIRONMENT=production

# Steam Integration
STEAM_API_KEY=your_production_steam_api_key

# Analytics (optional)
PUBLIC_ANALYTICS_ID=your_analytics_id

# Error Tracking (optional)
PUBLIC_SENTRY_DSN=your_sentry_dsn
```

## 🔑 Steam API Configuration

### Getting a Steam API Key

1. **Go to Steam Web API Key page**: https://steamcommunity.com/dev/apikey
2. **Sign in with your Steam account**
3. **Enter your domain name** (use `localhost` for development)
4. **Copy the generated API key**

### Steam API Key Setup

**For Development:**
```bash
# In both backend/.env and frontend/.env
STEAM_API_KEY=XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
```

**For Production:**
```bash
# Use the same key, but registered with your production domain
STEAM_API_KEY=XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
```

### Steam API Validation

```bash
# Test your Steam API key
curl "https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v0002/?key=YOUR_KEY&steamids=*****************"
```

## 🔒 Security Configuration

### JWT Configuration

**Development:**
```bash
# Simple secret for development
JWT_SECRET=development-secret-key-not-secure

# Short expiration for testing
JWT_EXPIRATION_HOURS=24
```

**Production:**
```bash
# Strong, random secret (minimum 32 characters)
JWT_SECRET=$(openssl rand -base64 32)

# Reasonable expiration
JWT_EXPIRATION_HOURS=168  # 1 week
```

### CORS Configuration

**Development (permissive):**
```bash
CORS_ORIGINS=["*"]
# Or specific origins:
CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173"]
```

**Production (restrictive):**
```bash
CORS_ORIGINS=["https://yourdomain.com"]
# Multiple domains:
CORS_ORIGINS=["https://yourdomain.com", "https://www.yourdomain.com"]
```

### SSL/TLS Configuration

**Generate Self-Signed Certificate (Development):**
```bash
# Create SSL directory
mkdir -p nginx/ssl

# Generate certificate
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout nginx/ssl/brainless-stats.key \
  -out nginx/ssl/brainless-stats.crt \
  -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
```

**Let's Encrypt Certificate (Production):**
```bash
# Install certbot
sudo apt install certbot

# Generate certificate
sudo certbot certonly --standalone -d yourdomain.com

# Link certificates
sudo ln -s /etc/letsencrypt/live/yourdomain.com/fullchain.pem nginx/ssl/brainless-stats.crt
sudo ln -s /etc/letsencrypt/live/yourdomain.com/privkey.pem nginx/ssl/brainless-stats.key
```

## 💾 Database Configuration

### SQLite Configuration (Default)

**Development:**
```bash
DATABASE_URL=sqlite:///./database.db
```

**Production:**
```bash
# Use absolute path for production
DATABASE_URL=sqlite:////app/data/database.db

# Enable WAL mode for better concurrent access
SQLITE_WAL_MODE=true
```

### PostgreSQL Configuration (Optional)

**Install PostgreSQL:**
```bash
# Ubuntu/Debian
sudo apt install postgresql postgresql-contrib

# Create database and user
sudo -u postgres createdb brainless_stats
sudo -u postgres createuser brainless_user
sudo -u postgres psql -c "ALTER USER brainless_user WITH PASSWORD 'secure_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE brainless_stats TO brainless_user;"
```

**PostgreSQL Configuration:**
```bash
DATABASE_URL=postgresql://brainless_user:secure_password@localhost/brainless_stats

# Connection pool settings
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
```

## 🚀 Redis Configuration

### Redis Setup

**Using Docker (Recommended):**
```bash
# Development
docker run -d -p 6379:6379 --name redis redis:7-alpine

# Production with persistence
docker run -d \
  -p 6379:6379 \
  -v redis_data:/data \
  --name redis \
  redis:7-alpine redis-server --appendonly yes
```

**Local Installation:**
```bash
# Ubuntu/Debian
sudo apt install redis-server

# macOS
brew install redis

# Start service
sudo systemctl start redis-server  # Linux
brew services start redis          # macOS
```

### Redis Configuration

**Basic Configuration:**
```bash
REDIS_URL=redis://localhost:6379

# With authentication
REDIS_URL=redis://:password@localhost:6379

# With specific database
REDIS_URL=redis://localhost:6379/0
```

**Advanced Redis Configuration:**
```bash
# Connection settings
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# Connection pool
REDIS_POOL_SIZE=10
REDIS_POOL_TIMEOUT=5

# Demo processing queue settings
REDIS_QUEUE_NAME=demo_processing
REDIS_RESULT_TTL=3600  # 1 hour
```

## 📁 File Storage Configuration

### Local File Storage

**Development:**
```bash
DEMO_STORAGE_PATH=./storage/demos
MAX_UPLOAD_SIZE=104857600  # 100MB
```

**Production:**
```bash
DEMO_STORAGE_PATH=/app/data/demos
MAX_UPLOAD_SIZE=209715200  # 200MB

# Ensure proper permissions
mkdir -p /app/data/demos
chown -R app:app /app/data
chmod 755 /app/data/demos
```

### Storage Structure

```bash
storage/
├── demos/                    # Original demo files
│   ├── 2024/
│   │   ├── 01/              # Organized by year/month
│   │   │   └── demo_123.dem
│   │   └── 02/
│   └── 2023/
├── processed/               # Processed data cache
│   ├── json/               # Extracted JSON data
│   └── stats/              # Computed statistics
└── backups/                # Regular backups
    ├── daily/
    └── weekly/
```

### File Storage Cleanup

**Automated Cleanup Script:**
```bash
#!/bin/bash
# cleanup-storage.sh

# Remove processed files older than 30 days
find /app/data/processed -type f -mtime +30 -delete

# Remove demo files older than 90 days (optional)
# find /app/data/demos -type f -mtime +90 -delete

# Clean empty directories
find /app/data -type d -empty -delete
```

## 🔍 Logging Configuration

### Log Levels

```bash
# Development
LOG_LEVEL=DEBUG

# Production
LOG_LEVEL=INFO

# Available levels: DEBUG, INFO, WARNING, ERROR, CRITICAL
```

### Log Formats

**Development (Human Readable):**
```bash
LOG_FORMAT=colored
```

**Production (Structured):**
```bash
LOG_FORMAT=json
```

### Log Rotation

**Docker Compose Logging:**
```yaml
# docker-compose.prod.yml
services:
  backend:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

## ⚡ Performance Configuration

### Demo Processing Performance

```bash
# Number of concurrent processing jobs
DEMO_PROCESSING_CONCURRENCY=4

# Processing timeout (seconds)
DEMO_PROCESSING_TIMEOUT=600

# Memory limit per processing job
DEMO_PROCESSING_MEMORY_LIMIT=2048  # MB
```

### Database Performance

**SQLite Optimizations:**
```bash
# Enable WAL mode
SQLITE_WAL_MODE=true

# Increase cache size
SQLITE_CACHE_SIZE=10000

# Connection timeout
SQLITE_TIMEOUT=30
```

**PostgreSQL Optimizations:**
```bash
# Connection pool settings
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=0
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
```

### Caching Configuration

```bash
# Redis cache settings
CACHE_TTL=3600           # 1 hour default TTL
CACHE_MAX_CONNECTIONS=10
CACHE_RETRY_ON_TIMEOUT=true

# Application-level caching
ENABLE_QUERY_CACHE=true
QUERY_CACHE_SIZE=1000
STATIC_CACHE_TTL=86400   # 24 hours
```

## 🔧 Advanced Configuration

### Rate Limiting

```bash
# Enable rate limiting
RATE_LIMIT_ENABLED=true

# General API limits
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Upload-specific limits
UPLOAD_RATE_LIMIT=10
UPLOAD_RATE_WINDOW=300   # 5 minutes

# Authentication limits
AUTH_RATE_LIMIT=20
AUTH_RATE_WINDOW=900     # 15 minutes
```

### Health Check Configuration

```bash
# Health check intervals
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# Service dependency checks
CHECK_DATABASE=true
CHECK_REDIS=true
CHECK_STEAM_API=true
CHECK_DISK_SPACE=true

# Disk space threshold
DISK_SPACE_THRESHOLD=90  # Percent
```

### Background Task Configuration

```bash
# Task queue settings
TASK_QUEUE_MAX_RETRIES=3
TASK_QUEUE_RETRY_DELAY=60

# Demo processing specific
DEMO_TASK_PRIORITY=5
DEMO_TASK_TTL=7200       # 2 hours

# Cleanup tasks
CLEANUP_INTERVAL=3600    # 1 hour
CLEANUP_OLD_TASKS=true
TASK_HISTORY_RETENTION=168  # 1 week
```

## 🐳 Docker Configuration

### Docker Compose Override

**docker-compose.override.yml (Development):**
```yaml
version: '3.8'
services:
  backend:
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
    volumes:
      - ./backend/src:/app/src
    
  frontend:
    environment:
      - NODE_ENV=development
    ports:
      - "3000:3000"
      - "24678:24678"  # Vite HMR
```

**docker-compose.prod.yml (Production):**
```yaml
version: '3.8'
services:
  backend:
    restart: unless-stopped
    environment:
      - ENV=production
      - DEBUG=false
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"
```

### Container Resource Limits

```yaml
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
  
  demo_processing:
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
```

## 📊 Monitoring Configuration

### Application Metrics

```bash
# Enable metrics collection
METRICS_ENABLED=true
METRICS_PORT=9090

# Prometheus endpoint
METRICS_ENDPOINT=/metrics

# Custom metrics
TRACK_PROCESSING_TIMES=true
TRACK_API_RESPONSE_TIMES=true
TRACK_UPLOAD_METRICS=true
```

### Error Tracking

```bash
# Sentry integration (optional)
SENTRY_DSN=your_sentry_dsn_here
SENTRY_ENVIRONMENT=production
SENTRY_TRACES_SAMPLE_RATE=0.1

# Error reporting
ENABLE_ERROR_REPORTING=true
ERROR_REPORT_EMAIL=<EMAIL>
```

## ✅ Configuration Validation

### Validation Script

```bash
#!/bin/bash
# validate-config.sh

echo "Validating Brainless Stats configuration..."

# Check required environment files
for file in .env backend/.env frontend/.env; do
  if [[ ! -f "$file" ]]; then
    echo "❌ Missing required file: $file"
    exit 1
  fi
done

# Check Steam API key
if [[ -z "$STEAM_API_KEY" ]]; then
  echo "❌ STEAM_API_KEY is not set"
  exit 1
fi

# Test Steam API connectivity
if ! curl -s "https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v0002/?key=$STEAM_API_KEY&steamids=*****************" > /dev/null; then
  echo "❌ Steam API key is invalid or Steam API is unreachable"
  exit 1
fi

# Check Redis connectivity
if ! redis-cli ping > /dev/null 2>&1; then
  echo "❌ Cannot connect to Redis"
  exit 1
fi

echo "✅ Configuration validation passed!"
```

### Environment Validation

```python
# backend/src/backend/config_validator.py
import os
import sys
from typing import List

def validate_config() -> List[str]:
    """Validate configuration and return list of errors."""
    errors = []
    
    # Required environment variables
    required_vars = [
        'DATABASE_URL',
        'REDIS_URL', 
        'STEAM_API_KEY',
        'JWT_SECRET'
    ]
    
    for var in required_vars:
        if not os.getenv(var):
            errors.append(f"Missing required environment variable: {var}")
    
    # Validate JWT secret strength
    jwt_secret = os.getenv('JWT_SECRET', '')
    if len(jwt_secret) < 32:
        errors.append("JWT_SECRET must be at least 32 characters long")
    
    # Validate file paths
    storage_path = os.getenv('DEMO_STORAGE_PATH', './storage/demos')
    if not os.path.exists(os.path.dirname(storage_path)):
        errors.append(f"Storage directory does not exist: {storage_path}")
    
    return errors

if __name__ == "__main__":
    errors = validate_config()
    if errors:
        print("❌ Configuration validation failed:")
        for error in errors:
            print(f"  - {error}")
        sys.exit(1)
    else:
        print("✅ Configuration validation passed!")
```

## 🔗 Next Steps

- **[Quick Start Guide](quick-start.md)** - Get the application running
- **[Installation Guide](installation.md)** - Detailed setup instructions
- **[Development Setup](../development/setup.md)** - Development environment
- **[Production Deployment](../deployment/production.md)** - Production configuration

---

**Your Brainless Stats instance is now properly configured! 🎯**