# Quick Start Guide

Get Brainless Stats up and running in under 10 minutes.

## Prerequisites

Ensure you have the following installed:

- **Node.js** 18+ and **pnpm** 8+
- **Python** 3.13+ and **uv**
- **Docker** and **Docker Compose**

## 🚀 Setup Commands

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository-url>
cd brainless-stats
pnpm setup
```

This will:
- Install all dependencies (frontend + backend)
- Setup Python virtual environments
- Initialize databases
- Copy environment configuration files

### 2. Start Development Environment

```bash
pnpm dev
```

This starts:
- **Frontend**: http://localhost:3000
- **Backend**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Redis**: localhost:6379

### 3. Alternative: Full Docker Environment

If you prefer Docker for everything:

```bash
pnpm dev:full
```

## 🎯 First Steps

### 1. Access the Application
Open http://localhost:3000 in your browser.

### 2. Steam Authentication
- Click "Login with Steam"
- Complete Steam OAuth flow
- You'll be redirected to settings for CS2 configuration

### 3. Configure CS2 Tracking
- Enter your **Authentication Code** from CS2 console (`status` command)
- Add an **Initial Match Token** (Steam share code)
- Save configuration

### 4. Upload Your First Demo
- Navigate to the "Demo Files" section
- Drag & drop a `.dem` file or click to browse
- Enter a match ID
- Wait for processing to complete

### 5. View Analysis
- Go to "Matches" to see processed demos
- Click on a match to view detailed analysis

## 🛠️ Development Commands

### Frontend Development
```bash
cd frontend
pnpm dev          # Start dev server
pnpm test         # Run tests
pnpm check        # Type checking
```

### Backend Development
```bash
cd backend
uv run brainless-cli dev      # Start with Docker
uv run pytest               # Run tests
uv run ruff check            # Lint code
```

### Full Stack Commands
```bash
pnpm test                    # All tests
pnpm lint                    # All linting
pnpm build                   # Build everything
```

## 📁 Project Structure Overview

```
brainless-stats/
├── frontend/               # SvelteKit app (port 3000)
├── backend/               # FastAPI app (port 8000)
│   └── demo_processing/   # Demo parsing microservice
├── nginx/                 # Production proxy
├── scripts/              # Development tools
└── docs/                 # This documentation
```

## 🔧 Environment Configuration

### Key Environment Variables

Create `.env` files in project root, frontend/, and backend/:

```bash
# Root .env
ENV=development
COMPOSE_PROJECT_NAME=brainless-stats

# Backend .env
DATABASE_URL=sqlite:///./database.db
REDIS_URL=redis://localhost:6379
STEAM_API_KEY=your_steam_api_key

# Frontend .env
PUBLIC_API_BASE_URL=http://localhost:8000
STEAM_API_KEY=your_steam_api_key
```

## 🚨 Common Issues

### Port Already in Use
```bash
# Check what's using the ports
lsof -i :3000
lsof -i :8000
lsof -i :6379

# Kill processes if needed
kill -9 <PID>
```

### Docker Issues
```bash
# Clean up Docker
pnpm clean
docker system prune -f

# Restart everything
pnpm dev
```

### Database Issues
```bash
# Reset databases
rm backend/database.db
rm frontend/local.db
pnpm dev
```

## 🎯 Next Steps

Once you have the basic setup working:

1. **[Configuration Guide](configuration.md)** - Detailed configuration options
2. **[First Demo Upload](first-demo.md)** - Step-by-step demo upload process
3. **[Development Setup](../development/setup.md)** - Advanced development configuration
4. **[Architecture Overview](../architecture/overview.md)** - Understanding the system

## 💡 Tips

- Use `pnpm logs` to view all service logs
- Check `pnpm health` to verify all services are running
- The API documentation at http://localhost:8000/docs is interactive
- Hot reloading works for both frontend and backend
- Database schema updates are automatic in development

## 🆘 Getting Help

If you run into issues:

1. Check the [Troubleshooting Guide](../operations/troubleshooting.md)
2. Search [GitHub Issues](https://github.com/maxnoller/brainless-stats/issues)
3. Ask in [GitHub Discussions](https://github.com/maxnoller/brainless-stats/discussions)

---

**You're now ready to start developing with Brainless Stats! 🎮**