# Installation Guide

Complete installation instructions for Brainless Stats development and production environments.

## System Requirements

### Development Environment

**Minimum Requirements:**
- **OS**: Linux, macOS, or Windows 10/11 with WSL2
- **RAM**: 4GB (8GB recommended)
- **Storage**: 2GB free space
- **CPU**: 2 cores (4 cores recommended)

**Software Requirements:**
- **Node.js**: 18.0.0 or higher
- **pnpm**: 8.0.0 or higher
- **Python**: 3.13.0 or higher
- **uv**: Latest version
- **Docker**: 20.10.0 or higher
- **Docker Compose**: 2.0.0 or higher

### Production Environment

**Minimum Requirements:**
- **OS**: Linux (Ubuntu 22.04+ or similar)
- **RAM**: 8GB (16GB recommended)
- **Storage**: 20GB free space (more for demo storage)
- **CPU**: 4 cores (8 cores recommended)

**Software Requirements:**
- **Docker**: 20.10.0 or higher
- **Docker Compose**: 2.0.0 or higher
- **Nginx**: 1.18.0 or higher (if not using Docker)

## Development Installation

### 1. Install System Dependencies

#### Ubuntu/Debian
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install base dependencies
sudo apt install -y curl wget git build-essential

# Install Node.js via NodeSource
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Install pnpm
npm install -g pnpm

# Install Python 3.13
sudo apt install -y software-properties-common
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt install -y python3.13 python3.13-venv python3.13-dev

# Install uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo apt install -y docker-compose-plugin
```

#### macOS
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install dependencies
brew install node pnpm python@3.13 uv docker docker-compose git
```

#### Windows (WSL2)
```bash
# Install WSL2 first, then follow Ubuntu instructions above

# Or use Windows package managers
winget install OpenJS.NodeJS
winget install pnpm.pnpm
winget install Python.Python.3.13
winget install Docker.DockerDesktop
```

### 2. Clone Repository

```bash
git clone https://github.com/maxnoller/brainless-stats.git
cd brainless-stats
```

### 3. Automated Setup

```bash
# Run the automated setup
pnpm setup
```

This script will:
- Install all Node.js dependencies
- Setup Python virtual environments
- Install Python dependencies
- Initialize databases
- Copy environment files
- Verify installation

### 4. Manual Setup (Alternative)

If automated setup fails, follow these manual steps:

#### Install Frontend Dependencies
```bash
cd frontend
pnpm install
cd ..
```

#### Setup Backend Environment
```bash
cd backend
# Create virtual environment
uv venv
# Activate virtual environment
source .venv/bin/activate  # Linux/macOS
# .venv\Scripts\activate.bat  # Windows

# Install dependencies
uv pip install -e .
uv pip install -e ./demo_processing
cd ..
```

#### Setup Environment Files
```bash
# Copy environment templates
cp .env.example .env
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# Edit environment files as needed
```

### 5. Verify Installation

```bash
# Test the installation
pnpm health

# Start development environment
pnpm dev

# Check if services are running
curl http://localhost:8000/health
curl http://localhost:3000
```

## Production Installation

### 1. Server Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo apt install -y docker-compose-plugin

# Create application user
sudo useradd -m -s /bin/bash brainless-stats
sudo usermod -aG docker brainless-stats
```

### 2. Deploy Application

```bash
# Switch to application user
sudo su - brainless-stats

# Clone repository
git clone https://github.com/maxnoller/brainless-stats.git
cd brainless-stats

# Setup production environment
cp .env.example .env
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# Edit environment files for production
nano .env
nano backend/.env
nano frontend/.env
```

### 3. Production Environment Configuration

Edit the environment files:

**.env**
```bash
ENV=production
COMPOSE_PROJECT_NAME=brainless-stats

# Security
SECRET_KEY=your-secret-key-here
JWT_SECRET=your-jwt-secret-here

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/brainless-stats.crt
SSL_KEY_PATH=/etc/ssl/private/brainless-stats.key
```

**backend/.env**
```bash
ENV=production
DATABASE_URL=sqlite:///./data/database.db
REDIS_URL=redis://redis:6379

# Steam API
STEAM_API_KEY=your-steam-api-key

# Security
CORS_ORIGINS=["https://yourdomain.com"]
DEMO_STORAGE_PATH=/app/data/demos
```

**frontend/.env**
```bash
PUBLIC_API_BASE_URL=https://yourdomain.com/api
PUBLIC_ENVIRONMENT=production
```

### 4. SSL Certificate Setup

```bash
# Create SSL directory
sudo mkdir -p /etc/ssl/certs /etc/ssl/private

# Option 1: Self-signed certificate (development)
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout /etc/ssl/private/brainless-stats.key \
  -out /etc/ssl/certs/brainless-stats.crt

# Option 2: Let's Encrypt (production)
sudo apt install -y certbot
sudo certbot certonly --standalone -d yourdomain.com
sudo ln -s /etc/letsencrypt/live/yourdomain.com/fullchain.pem /etc/ssl/certs/brainless-stats.crt
sudo ln -s /etc/letsencrypt/live/yourdomain.com/privkey.pem /etc/ssl/private/brainless-stats.key
```

### 5. Start Production Services

```bash
# Build and start services
pnpm build:docker
pnpm start:prod

# Verify services are running
pnpm status
pnpm health
```

## Environment Variables Reference

### Global Variables (.env)

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `ENV` | Environment mode | `development` | Yes |
| `COMPOSE_PROJECT_NAME` | Docker project name | `brainless-stats` | Yes |
| `SSL_CERT_PATH` | SSL certificate path | - | Production |
| `SSL_KEY_PATH` | SSL private key path | - | Production |

### Backend Variables (backend/.env)

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DATABASE_URL` | Database connection | `sqlite:///./database.db` | Yes |
| `REDIS_URL` | Redis connection | `redis://localhost:6379` | Yes |
| `STEAM_API_KEY` | Steam Web API key | - | Yes |
| `JWT_SECRET` | JWT signing key | - | Yes |
| `CORS_ORIGINS` | Allowed CORS origins | `["*"]` | Yes |
| `DEMO_STORAGE_PATH` | Demo file storage | `./storage/demos` | Yes |
| `LOG_LEVEL` | Logging level | `INFO` | No |

### Frontend Variables (frontend/.env)

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `PUBLIC_API_BASE_URL` | Backend API URL | `http://localhost:8000` | Yes |
| `PUBLIC_ENVIRONMENT` | Environment name | `development` | Yes |
| `STEAM_API_KEY` | Steam Web API key | - | Yes |

## Troubleshooting Installation

### Common Issues

#### Node.js Version Issues
```bash
# Check Node.js version
node --version

# Use Node Version Manager
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18
```

#### Python Version Issues
```bash
# Check Python version
python3.13 --version

# Use pyenv for version management
curl https://pyenv.run | bash
pyenv install 3.13.0
pyenv global 3.13.0
```

#### Docker Permission Issues
```bash
# Add user to docker group
sudo usermod -aG docker $USER

# Restart shell or logout/login
newgrp docker
```

#### Port Conflicts
```bash
# Find processes using ports
lsof -i :3000
lsof -i :8000
lsof -i :6379

# Change ports in environment files
# Or kill conflicting processes
```

#### uv Installation Issues
```bash
# Manual uv installation
curl -LsSf https://astral.sh/uv/install.sh | sh
source ~/.bashrc

# Or use pip
pip install uv
```

### Verification Commands

```bash
# Check all prerequisites
node --version     # Should be 18+
pnpm --version     # Should be 8+
python3.13 --version  # Should be 3.13+
uv --version       # Should show version
docker --version   # Should be 20.10+
docker-compose --version  # Should be 2.0+

# Test Docker
docker run hello-world

# Test the application
pnpm health
```

## Next Steps

After successful installation:

1. **[Configuration Guide](configuration.md)** - Configure your environment
2. **[Quick Start Guide](quick-start.md)** - Get the application running
3. **[Development Setup](../development/setup.md)** - Advanced development configuration

## Getting Help

If you encounter issues during installation:

1. Check the [Troubleshooting Guide](../operations/troubleshooting.md)
2. Search [GitHub Issues](https://github.com/maxnoller/brainless-stats/issues)
3. Create a new issue with:
   - Operating system details
   - Software versions
   - Complete error messages
   - Steps to reproduce

---

**Installation complete! Ready to start developing! 🚀**