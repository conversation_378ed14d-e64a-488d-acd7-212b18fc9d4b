# Frontend Overview

Comprehensive guide to the Brainless Stats SvelteKit frontend application.

## 🏗️ Architecture Overview

The frontend is built with SvelteKit and follows modern web development practices with a focus on performance, accessibility, and developer experience.

```mermaid
graph TB
    subgraph "SvelteKit Application"
        Routes[Route Pages]
        Layouts[Layout Components]
        Components[Reusable Components]
        Stores[Svelte Stores]
        Utils[Utility Functions]
    end
    
    subgraph "External Services"
        API[Backend API]
        Steam[Steam API]
        Auth[Authentication]
    end
    
    subgraph "UI Framework"
        Tailwind[Tailwind CSS]
        Shadcn[shadcn/ui Components]
        Icons[Lucide Icons]
    end
    
    Routes --> Layouts
    Layouts --> Components
    Components --> Stores
    Components --> Utils
    Routes --> API
    Auth --> Steam
    Components --> Tailwind
    Components --> Shadcn
    Components --> Icons
```

## 📁 Project Structure

```
frontend/
├── src/
│   ├── lib/                          # Shared libraries and utilities
│   │   ├── api/                      # API client and types
│   │   │   ├── index.ts              # Main API client
│   │   │   └── schema.d.ts           # Generated API types
│   │   ├── components/               # Reusable UI components
│   │   │   ├── ui/                   # shadcn/ui components
│   │   │   ├── AppBar.svelte         # Application header
│   │   │   ├── Logo.svelte           # Brand logo
│   │   │   └── app-sidebar.svelte    # Navigation sidebar
│   │   ├── hooks/                    # Svelte hooks and utilities
│   │   ├── icons/                    # Custom icon components
│   │   ├── server/                   # Server-side utilities
│   │   │   └── db/                   # Database utilities (frontend)
│   │   ├── types/                    # TypeScript type definitions
│   │   ├── config.ts                 # Configuration management
│   │   ├── jwt.ts                    # JWT handling
│   │   ├── steam_auth.ts             # Steam authentication
│   │   ├── users.ts                  # User management
│   │   └── utils.ts                  # General utilities
│   ├── routes/                       # SvelteKit routes
│   │   ├── (app)/                    # Authenticated app routes
│   │   │   ├── dashboard/            # User dashboard
│   │   │   ├── demos/                # Demo management
│   │   │   ├── matches/              # Match analysis
│   │   │   └── settings/             # User settings
│   │   ├── (public)/                 # Public routes
│   │   ├── login/                    # Authentication
│   │   ├── steam-callback/           # Steam OAuth callback
│   │   └── health/                   # Health check endpoint
│   ├── app.html                      # HTML template
│   ├── app.css                       # Global styles
│   └── hooks.server.ts               # Server-side hooks
├── static/                           # Static assets
├── tests/                            # Test files
├── Dockerfile.dev                    # Development Docker image
├── Dockerfile.prod                   # Production Docker image
├── package.json                      # Dependencies and scripts
├── svelte.config.js                  # SvelteKit configuration
├── tailwind.config.ts                # Tailwind CSS configuration
├── tsconfig.json                     # TypeScript configuration
└── vite.config.ts                    # Vite build configuration
```

## 🧩 Core Technologies

### SvelteKit
- **Framework**: SvelteKit 2.x for full-stack web applications
- **Rendering**: Server-side rendering with hydration
- **Routing**: File-based routing with layouts
- **Build Tool**: Vite for fast development and optimized builds

### TypeScript
- **Type Safety**: Strict TypeScript configuration
- **API Types**: Auto-generated types from OpenAPI schema
- **Component Props**: Strongly typed component interfaces

### Styling & UI
- **CSS Framework**: Tailwind CSS for utility-first styling
- **Component Library**: shadcn/ui for consistent design system
- **Icons**: Lucide icons for scalable vector graphics
- **Responsive Design**: Mobile-first responsive layouts

### State Management
- **Svelte Stores**: Reactive state management
- **Form Handling**: SvelteKit superforms with Zod validation
- **API State**: Custom stores for API data management

## 🛣️ Routing Structure

### Route Groups

```
routes/
├── (app)/                    # Authenticated application routes
│   ├── +layout.svelte        # App layout with sidebar
│   ├── +layout.server.ts     # Authentication check
│   ├── dashboard/            # Main dashboard
│   ├── demos/                # Demo file management
│   ├── matches/              # Match analysis and viewing
│   └── settings/             # User settings and configuration
├── (public)/                 # Public routes
│   ├── +page.svelte          # Landing page
│   └── +page.server.ts       # Public page data
├── login/                    # Authentication pages
├── steam-callback/           # OAuth callback handling
└── health/                   # Health check endpoint
```

### Route Protection

```typescript
// +layout.server.ts - Route protection
export const load: LayoutServerLoad = async ({ cookies, url }) => {
  const token = cookies.get('auth_token');
  
  if (!token && !isPublicRoute(url.pathname)) {
    throw redirect(302, '/login');
  }
  
  return {
    user: await getUserFromToken(token)
  };
};
```

## 🎨 Design System

### Component Hierarchy

```mermaid
graph TB
    App[App Root]
    Layout[Layout Components]
    Pages[Page Components]
    Features[Feature Components]
    UI[UI Components]
    
    App --> Layout
    Layout --> Pages
    Pages --> Features
    Features --> UI
    
    Layout --> Sidebar[app-sidebar.svelte]
    Layout --> AppBar[AppBar.svelte]
    
    Pages --> Dashboard[Dashboard Page]
    Pages --> Demos[Demos Page]
    Pages --> Matches[Matches Page]
    
    Features --> UploadForm[Demo Upload Form]
    Features --> MatchTable[Match Table]
    Features --> StatsCards[Statistics Cards]
    
    UI --> Button[Button]
    UI --> Card[Card]
    UI --> Table[Table]
```

### UI Component Library

The application uses shadcn/ui components for consistency:

```typescript
// Example component usage
import * as Card from '$lib/components/ui/card';
import { Button } from '$lib/components/ui/button';
import * as Table from '$lib/components/ui/table';

// In Svelte component
<Card.Root>
  <Card.Header>
    <Card.Title>Match Statistics</Card.Title>
  </Card.Header>
  <Card.Content>
    <Table.Root>
      <!-- Table content -->
    </Table.Root>
  </Card.Content>
</Card.Root>
```

### Theming System

```css
/* app.css - CSS variables for theming */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  /* ... more theme variables */
}

[data-theme="dark"] {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  /* ... dark theme overrides */
}
```

## 🔄 State Management

### Svelte Stores

```typescript
// lib/stores/auth.ts - Authentication store
import { writable } from 'svelte/store';
import type { User } from '$lib/types/api';

export const user = writable<User | null>(null);
export const isAuthenticated = writable(false);

// lib/stores/demos.ts - Demo management store
export const demos = writable<Demo[]>([]);
export const selectedDemo = writable<Demo | null>(null);
export const uploadProgress = writable(0);
```

### Form State Management

```typescript
// Using superforms for form handling
import { superForm } from 'sveltekit-superforms/client';
import { zod } from 'sveltekit-superforms/adapters';
import { z } from 'zod';

const uploadSchema = z.object({
  matchId: z.string().min(1),
  demoFile: z.instanceof(File)
});

const { form, enhance, errors } = superForm(data.form, {
  validators: zod(uploadSchema),
  onResult: ({ result }) => {
    if (result.type === 'success') {
      // Handle success
    }
  }
});
```

## 🌐 API Integration

### API Client

```typescript
// lib/api/index.ts - API client setup
import createClient from 'openapi-fetch';
import type { paths } from './schema';

const client = createClient<paths>({ 
  baseUrl: PUBLIC_API_BASE_URL 
});

// Authenticated requests
client.use({
  onRequest({ request }) {
    const token = getAuthToken();
    if (token) {
      request.headers.set('Authorization', `Bearer ${token}`);
    }
  }
});

export default client;
```

### Type-Safe API Calls

```typescript
// Using generated types for API calls
const { data, error } = await client.GET('/api/matches', {
  params: {
    query: {
      limit: 10,
      offset: 0
    }
  }
});

if (error) {
  console.error('API Error:', error);
  return;
}

// data is fully typed based on OpenAPI schema
console.log(data.length); // TypeScript knows this is an array
```

### Real-time Updates

```typescript
// WebSocket integration for real-time updates
import { browser } from '$app/environment';

let ws: WebSocket;

if (browser) {
  ws = new WebSocket('ws://localhost:8000/ws');
  
  ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    
    if (data.type === 'demo_processing_update') {
      updateDemoProgress(data.demo_id, data.progress);
    }
  };
}
```

## 🔐 Authentication Flow

### Steam OAuth Integration

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant Steam
    
    User->>Frontend: Click "Login with Steam"
    Frontend->>Backend: GET /auth/steam/login
    Backend->>Steam: Redirect to Steam OpenID
    Steam->>User: Steam login form
    User->>Steam: Enter credentials
    Steam->>Backend: POST callback with user data
    Backend->>Frontend: Set auth cookie + redirect
    Frontend->>User: Show authenticated app
```

### Authentication Components

```svelte
<!-- login/+page.svelte -->
<script lang="ts">
  import { goto } from '$app/navigation';
  
  async function handleSteamLogin() {
    const response = await fetch('/auth/steam/login');
    const { login_url } = await response.json();
    window.location.href = login_url;
  }
</script>

<Button on:click={handleSteamLogin}>
  <SteamIcon class="mr-2" />
  Login with Steam
</Button>
```

## 📱 Responsive Design

### Mobile-First Approach

```svelte
<!-- Responsive demo upload form -->
<div class="grid gap-6 lg:grid-cols-2">
  <!-- Upload form - full width on mobile, half on desktop -->
  <Card.Root class="lg:col-span-1">
    <Card.Header>
      <Card.Title>Upload Demo</Card.Title>
    </Card.Header>
    <Card.Content>
      <!-- Form content -->
    </Card.Content>
  </Card.Root>
  
  <!-- Recent activity - stacked on mobile -->
  <Card.Root class="lg:col-span-1">
    <Card.Header>
      <Card.Title>Recent Activity</Card.Title>
    </Card.Header>
    <Card.Content>
      <!-- Activity content -->
    </Card.Content>
  </Card.Root>
</div>
```

### Breakpoint System

```typescript
// lib/hooks/is-mobile.svelte.ts
import { browser } from '$app/environment';

export function useIsMobile() {
  let isMobile = $state(false);
  
  if (browser) {
    const mediaQuery = window.matchMedia('(max-width: 768px)');
    isMobile = mediaQuery.matches;
    
    mediaQuery.addEventListener('change', (e) => {
      isMobile = e.matches;
    });
  }
  
  return () => isMobile;
}
```

## 🎭 Component Examples

### Demo Upload Component

```svelte
<!-- Feature component example -->
<script lang="ts">
  import { superForm } from 'sveltekit-superforms/client';
  import * as Form from '$lib/components/ui/form';
  import { Button } from '$lib/components/ui/button';
  import { Upload } from 'lucide-svelte';
  
  export let data;
  
  const form = superForm(data.form, {
    validators: zod(uploadSchema),
    onResult: ({ result }) => {
      if (result.type === 'success') {
        // Handle successful upload
      }
    }
  });
  
  const { form: formData, enhance } = form;
  
  let isDragOver = $state(false);
  
  function handleDrop(event: DragEvent) {
    event.preventDefault();
    isDragOver = false;
    
    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      $formData.demoFile = files;
    }
  }
</script>

<form method="POST" use:enhance>
  <div 
    class="border-2 border-dashed rounded-lg p-6 transition-colors"
    class:border-primary={isDragOver}
    ondragover={(e) => { e.preventDefault(); isDragOver = true; }}
    ondragleave={() => isDragOver = false}
    ondrop={handleDrop}
  >
    <div class="text-center">
      <Upload class="mx-auto h-12 w-12 text-muted-foreground" />
      <p class="mt-2 text-sm">Drop your demo file here</p>
    </div>
    
    <input 
      type="file" 
      accept=".dem"
      bind:files={$formData.demoFile}
      class="sr-only" 
    />
  </div>
  
  <Button type="submit" class="w-full mt-4">
    Upload Demo
  </Button>
</form>
```

### Match Statistics Card

```svelte
<!-- UI component example -->
<script lang="ts">
  import * as Card from '$lib/components/ui/card';
  import { Badge } from '$lib/components/ui/badge';
  import type { Match } from '$lib/types/api';
  
  export let match: Match;
  
  $: winnerTeam = match.team_scores.team_a > match.team_scores.team_b ? 'A' : 'B';
</script>

<Card.Root class="hover:shadow-md transition-shadow">
  <Card.Header class="pb-3">
    <div class="flex items-center justify-between">
      <Card.Title class="text-lg">{match.match_id}</Card.Title>
      <Badge variant="secondary">{match.map_name}</Badge>
    </div>
  </Card.Header>
  
  <Card.Content>
    <div class="grid grid-cols-3 gap-4 text-center">
      <div>
        <p class="text-2xl font-bold">{match.team_scores.team_a}</p>
        <p class="text-sm text-muted-foreground">Team A</p>
      </div>
      
      <div class="flex items-center justify-center">
        <span class="text-muted-foreground">vs</span>
      </div>
      
      <div>
        <p class="text-2xl font-bold">{match.team_scores.team_b}</p>
        <p class="text-sm text-muted-foreground">Team B</p>
      </div>
    </div>
    
    <div class="mt-4 pt-4 border-t">
      <p class="text-sm text-muted-foreground">
        Winner: Team {winnerTeam} • {match.round_count} rounds
      </p>
    </div>
  </Card.Content>
</Card.Root>
```

## 🧪 Testing Strategy

### Component Testing

```typescript
// tests/components/Button.test.ts
import { render, screen } from '@testing-library/svelte';
import { Button } from '$lib/components/ui/button';

test('renders button with text', () => {
  render(Button, { children: 'Click me' });
  expect(screen.getByRole('button')).toHaveTextContent('Click me');
});
```

### E2E Testing

```typescript
// tests/demo-upload.spec.ts
import { test, expect } from '@playwright/test';

test('demo upload flow', async ({ page }) => {
  await page.goto('/demos');
  
  // Upload demo file
  await page.setInputFiles('input[type="file"]', 'test-demo.dem');
  await page.fill('input[name="matchId"]', 'TEST_MATCH');
  await page.click('button[type="submit"]');
  
  // Verify success
  await expect(page.locator('.success-message')).toBeVisible();
});
```

## 🚀 Performance Optimization

### Code Splitting

```typescript
// Dynamic imports for route-based code splitting
const LazyDashboard = lazy(() => import('./Dashboard.svelte'));
const LazyMatches = lazy(() => import('./Matches.svelte'));
```

### Image Optimization

```svelte
<script>
  import { dev } from '$app/environment';
</script>

<!-- Optimized image loading -->
<img 
  src={dev ? '/dev-avatar.jpg' : '/optimized-avatar.webp'}
  alt="User avatar"
  loading="lazy"
  width={48}
  height={48}
/>
```

### Bundle Analysis

```bash
# Analyze bundle size
pnpm run build
pnpm run analyze
```

## 🔗 Related Documentation

- **[Component Library](components.md)** - Detailed component documentation
- **[State Management](state-management.md)** - State handling patterns
- **[Routing & Navigation](routing.md)** - SvelteKit routing details
- **[Styling & Theming](styling.md)** - CSS and theme system

---

**Modern, type-safe, and performant frontend built with SvelteKit! 🎨⚡**