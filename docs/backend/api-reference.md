# Backend API Reference

Complete reference for the Brainless Stats FastAPI backend service.

## 🌐 Base URL

- **Development**: `http://localhost:8000`
- **Production**: `https://yourdomain.com/api`

## 📋 API Overview

The Brainless Stats API follows RESTful conventions and provides endpoints for:

- User authentication and management
- Demo file upload and management
- Match data retrieval and analysis
- Steam integration and tracking
- Background task monitoring

## 🔐 Authentication

### Steam OAuth Flow

The API uses Steam OpenID for user authentication:

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Steam
    
    Client->>API: GET /auth/steam/login
    API->>Steam: Redirect to Steam OpenID
    Steam->>Client: Steam login page
    Client->>Steam: User credentials
    Steam->>API: User data + verification
    API->>Client: JWT token + user info
```

### JWT Authentication

All protected endpoints require a valid JWT token:

```http
Authorization: Bearer <jwt_token>
```

## 📚 API Endpoints

### Health & Status

#### Get API Health
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "services": {
    "database": "healthy",
    "redis": "healthy",
    "demo_processing": "healthy"
  }
}
```

---

### Authentication Endpoints

#### Initiate Steam Login
```http
GET /auth/steam/login
```

**Query Parameters:**
- `return_url` (optional): URL to redirect after successful login

**Response:**
```json
{
  "login_url": "https://steamcommunity.com/openid/login?...",
  "state": "random_state_token"
}
```

#### Steam Login Callback
```http
GET /auth/steam/callback
```

**Query Parameters:**
- Steam OpenID response parameters

**Response:**
```json
{
  "access_token": "jwt_token_here",
  "token_type": "bearer",
  "expires_in": 3600,
  "user": {
    "id": 1,
    "steam_id": "76561198000000000",
    "username": "PlayerName",
    "profile_picture_url": "https://...",
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

#### Get Current User
```http
GET /auth/me
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "id": 1,
  "steam_id": "76561198000000000",
  "username": "PlayerName",
  "profile_picture_url": "https://...",
  "created_at": "2024-01-15T10:30:00Z",
  "tracking_details": {
    "auth_code": "XXXX-XXXXX-XXXX",
    "match_token": "CSGO-XXXXX-XXXXX-XXXXX-XXXXX-XXXXX",
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

---

### User Management

#### Get User by Steam ID
```http
GET /api/users/steam/{steam_id}
```

**Path Parameters:**
- `steam_id`: Steam ID 64-bit format

**Response:**
```json
{
  "id": 1,
  "steam_id": "76561198000000000",
  "username": "PlayerName",
  "profile_picture_url": "https://...",
  "created_at": "2024-01-15T10:30:00Z"
}
```

#### Update User Tracking Details
```http
POST /api/users/{user_id}/tracking
```

**Path Parameters:**
- `user_id`: User ID

**Request Body:**
```json
{
  "auth_code": "XXXX-XXXXX-XXXX",
  "match_token": "CSGO-XXXXX-XXXXX-XXXXX-XXXXX-XXXXX"
}
```

**Response:**
```json
{
  "id": 1,
  "user_id": 1,
  "auth_code": "XXXX-XXXXX-XXXX",
  "match_token": "CSGO-XXXXX-XXXXX-XXXXX-XXXXX-XXXXX",
  "created_at": "2024-01-15T10:30:00Z"
}
```

---

### Demo File Management

#### Upload Demo File
```http
POST /api/demos/upload
```

**Query Parameters:**
- `match_id`: Unique identifier for the match

**Request Body (multipart/form-data):**
- `demo_file`: Demo file (.dem format)

**Response:**
```json
{
  "id": 1,
  "match_id": "MATCH_2024_001",
  "file_path": "/storage/demos/2024/01/match_123_20240115.dem",
  "status": "DOWNLOADED",
  "error_message": null,
  "created_at": "2024-01-15T10:30:00Z"
}
```

**Status Values:**
- `DOWNLOADED`: File uploaded successfully
- `PROCESSING`: Demo being processed
- `PROCESSED`: Processing completed successfully
- `FAILED`: Processing failed

#### List Demo Files
```http
GET /api/demos
```

**Query Parameters:**
- `limit` (optional): Number of results (default: 50)
- `offset` (optional): Pagination offset (default: 0)
- `status` (optional): Filter by status

**Response:**
```json
[
  {
    "id": 1,
    "match_id": "MATCH_2024_001",
    "file_path": "/storage/demos/2024/01/match_123_20240115.dem",
    "status": "PROCESSED",
    "error_message": null,
    "created_at": "2024-01-15T10:30:00Z"
  }
]
```

#### Get Demo File Details
```http
GET /api/demos/{demo_id}
```

**Path Parameters:**
- `demo_id`: Demo file ID

**Response:**
```json
{
  "id": 1,
  "match_id": "MATCH_2024_001",
  "file_path": "/storage/demos/2024/01/match_123_20240115.dem",
  "status": "PROCESSED",
  "error_message": null,
  "created_at": "2024-01-15T10:30:00Z",
  "processing_log": [
    {
      "timestamp": "2024-01-15T10:31:00Z",
      "status": "PROCESSING",
      "message": "Started demo processing"
    },
    {
      "timestamp": "2024-01-15T10:35:00Z",
      "status": "PROCESSED",
      "message": "Processing completed successfully"
    }
  ]
}
```

#### Get Demo File by Match ID
```http
GET /api/demos/match/{match_id}
```

**Path Parameters:**
- `match_id`: Match identifier

**Response:**
```json
{
  "id": 1,
  "match_id": "MATCH_2024_001",
  "file_path": "/storage/demos/2024/01/match_123_20240115.dem",
  "status": "PROCESSED",
  "error_message": null,
  "created_at": "2024-01-15T10:30:00Z"
}
```

---

### Match Data

#### List Matches
```http
GET /api/matches
```

**Query Parameters:**
- `limit` (optional): Number of results (default: 50)
- `offset` (optional): Pagination offset (default: 0)
- `map_name` (optional): Filter by map name
- `date_from` (optional): Filter matches from date (ISO format)
- `date_to` (optional): Filter matches to date (ISO format)

**Response:**
```json
[
  {
    "id": 1,
    "demo_file_id": 1,
    "match_id": "MATCH_2024_001",
    "map_name": "de_dust2",
    "match_date": "2024-01-15T20:30:00Z",
    "duration": 2847,
    "team_scores": {
      "team_a": 16,
      "team_b": 14
    },
    "player_count": 10,
    "round_count": 30,
    "created_at": "2024-01-15T10:35:00Z"
  }
]
```

#### Get Match Details
```http
GET /api/matches/{match_id}
```

**Path Parameters:**
- `match_id`: Match ID

**Response:**
```json
{
  "id": 1,
  "demo_file_id": 1,
  "match_id": "MATCH_2024_001",
  "map_name": "de_dust2",
  "match_date": "2024-01-15T20:30:00Z",
  "duration": 2847,
  "team_scores": {
    "team_a": 16,
    "team_b": 14
  },
  "teams": [
    {
      "side": "terrorist",
      "name": "Team A",
      "score": 16,
      "players": [
        {
          "steam_id": "76561198000000001",
          "name": "Player1",
          "kills": 23,
          "deaths": 18,
          "assists": 7,
          "adr": 85.4,
          "kd_ratio": 1.28,
          "rating": 1.15
        }
      ]
    }
  ],
  "rounds": [
    {
      "round_number": 1,
      "winner": "terrorist",
      "end_reason": "bomb_exploded",
      "duration": 98,
      "events": [
        {
          "type": "kill",
          "timestamp": 15.3,
          "killer": "Player1",
          "victim": "Player2",
          "weapon": "ak47",
          "headshot": true
        }
      ]
    }
  ],
  "created_at": "2024-01-15T10:35:00Z"
}
```

#### Get Match Statistics
```http
GET /api/matches/{match_id}/stats
```

**Path Parameters:**
- `match_id`: Match ID

**Query Parameters:**
- `type` (optional): Statistics type (`player`, `team`, `weapon`, `round`)

**Response:**
```json
{
  "match_id": "MATCH_2024_001",
  "map_name": "de_dust2",
  "player_stats": [
    {
      "steam_id": "76561198000000001",
      "name": "Player1",
      "team": "terrorist",
      "kills": 23,
      "deaths": 18,
      "assists": 7,
      "adr": 85.4,
      "kd_ratio": 1.28,
      "rating": 1.15,
      "headshot_percentage": 45.2,
      "accuracy": 23.8,
      "mvp_count": 8
    }
  ],
  "team_stats": [
    {
      "side": "terrorist",
      "rounds_won": 16,
      "rounds_lost": 14,
      "total_kills": 95,
      "total_deaths": 89,
      "bomb_plants": 12,
      "bomb_defuses": 3
    }
  ],
  "weapon_stats": [
    {
      "weapon": "ak47",
      "kills": 28,
      "headshots": 12,
      "accuracy": 24.1
    }
  ]
}
```

---

### Steam Integration

#### Download Match from Steam
```http
POST /api/steam/download-match
```

**Request Body:**
```json
{
  "share_code": "CSGO-XXXXX-XXXXX-XXXXX-XXXXX-XXXXX",
  "auth_code": "XXXX-XXXXX-XXXX"
}
```

**Response:**
```json
{
  "job_id": "processing_job_123",
  "status": "queued",
  "message": "Match download started",
  "estimated_time": 300
}
```

#### Get Steam Match Info
```http
GET /api/steam/match-info
```

**Query Parameters:**
- `share_code`: Steam share code
- `auth_code`: User authentication code

**Response:**
```json
{
  "match_id": "3441234567890123456",
  "map_name": "de_dust2",
  "match_date": "2024-01-15T20:30:00Z",
  "duration": 2847,
  "players": [
    {
      "steam_id": "76561198000000001",
      "name": "Player1",
      "score": 23
    }
  ],
  "download_url": "https://...",
  "available": true
}
```

---

### Background Tasks

#### Get Task Status
```http
GET /api/tasks/{task_id}
```

**Path Parameters:**
- `task_id`: Background task ID

**Response:**
```json
{
  "id": "processing_job_123",
  "type": "demo_processing",
  "status": "running",
  "progress": 65,
  "message": "Processing rounds 20/30",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:33:00Z",
  "result": null,
  "error": null
}
```

**Status Values:**
- `queued`: Task is waiting to be processed
- `running`: Task is currently being processed
- `completed`: Task completed successfully
- `failed`: Task failed with error

#### List User Tasks
```http
GET /api/tasks
```

**Query Parameters:**
- `status` (optional): Filter by task status
- `type` (optional): Filter by task type
- `limit` (optional): Number of results (default: 50)

**Response:**
```json
[
  {
    "id": "processing_job_123",
    "type": "demo_processing",
    "status": "completed",
    "progress": 100,
    "message": "Processing completed successfully",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:35:00Z"
  }
]
```

---

## 📊 Response Formats

### Success Response
```json
{
  "data": {
    // Response data
  },
  "meta": {
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456789"
  }
}
```

### Error Response
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request data",
    "details": [
      {
        "field": "match_id",
        "message": "Match ID is required"
      }
    ]
  },
  "meta": {
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456789"
  }
}
```

### Pagination Response
```json
{
  "data": [
    // Array of items
  ],
  "pagination": {
    "total": 150,
    "page": 1,
    "per_page": 50,
    "pages": 3,
    "has_next": true,
    "has_prev": false
  }
}
```

## 🚨 Error Codes

| Code | Status | Description |
|------|--------|-------------|
| `VALIDATION_ERROR` | 400 | Request validation failed |
| `UNAUTHORIZED` | 401 | Authentication required |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `CONFLICT` | 409 | Resource conflict |
| `RATE_LIMITED` | 429 | Rate limit exceeded |
| `INTERNAL_ERROR` | 500 | Internal server error |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable |

## 🔄 Rate Limiting

API endpoints are rate-limited to prevent abuse:

- **General endpoints**: 100 requests per minute
- **Upload endpoints**: 10 requests per minute
- **Authentication endpoints**: 20 requests per minute

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248000
```

## 🔌 WebSocket Events

### Connection
```javascript
const ws = new WebSocket('ws://localhost:8000/ws');
```

### Demo Processing Events
```json
{
  "type": "demo_processing_update",
  "data": {
    "demo_id": 1,
    "status": "processing",
    "progress": 45,
    "message": "Processing round 15/30"
  }
}
```

### Match Analysis Events
```json
{
  "type": "match_analysis_complete",
  "data": {
    "match_id": "MATCH_2024_001",
    "status": "completed",
    "result_url": "/api/matches/MATCH_2024_001"
  }
}
```

## 📝 OpenAPI Specification

The complete OpenAPI specification is available at:
- **Interactive Documentation**: http://localhost:8000/docs
- **ReDoc Documentation**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## 🧪 Testing the API

### Using curl
```bash
# Get API health
curl http://localhost:8000/health

# Upload demo file
curl -X POST \
  "http://localhost:8000/api/demos/upload?match_id=TEST_MATCH" \
  -H "Authorization: Bearer <token>" \
  -F "demo_file=@demo.dem"

# Get matches
curl -H "Authorization: Bearer <token>" \
  http://localhost:8000/api/matches
```

### Using Python
```python
import requests

# Authentication
response = requests.get('http://localhost:8000/auth/steam/login')
login_url = response.json()['login_url']

# Upload demo
files = {'demo_file': open('demo.dem', 'rb')}
params = {'match_id': 'TEST_MATCH'}
headers = {'Authorization': 'Bearer <token>'}

response = requests.post(
    'http://localhost:8000/api/demos/upload',
    files=files,
    params=params,
    headers=headers
)
```

## 🔗 Related Documentation

- **[Backend Overview](overview.md)** - Backend architecture overview
- **[Database Operations](database.md)** - Database models and operations
- **[Error Handling](error-handling.md)** - Error handling patterns
- **[Background Tasks](background-tasks.md)** - Async processing documentation

---

**Complete API reference for building amazing CS2 analysis applications! 🎮⚡**