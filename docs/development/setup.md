# Development Setup Guide

Complete guide for setting up a development environment for Brainless Stats.

## 🛠️ Prerequisites

Before starting, ensure you have the required tools installed. See the [Installation Guide](../getting-started/installation.md) for detailed instructions.

**Required Software:**
- **Node.js** 18+ with **pnpm** 8+
- **Python** 3.13+ with **uv**
- **Docker** & **Docker Compose**
- **Git**

**Recommended Tools:**
- **VS Code** with extensions (see [IDE Setup](#ide-setup))
- **Redis CLI** for debugging
- **Postman** or **curl** for API testing

## 🚀 Quick Development Setup

### 1. Clone and Initialize

```bash
# Clone the repository
git clone https://github.com/maxnoller/brainless-stats.git
cd brainless-stats

# Run automated setup
pnpm setup
```

The setup script will:
- Install all Node.js dependencies
- Setup Python virtual environments
- Install Python dependencies
- Initialize databases
- Copy environment files
- Verify installation

### 2. Start Development Environment

```bash
# Start all services (recommended)
pnpm dev

# Or start services individually
pnpm dev:frontend    # SvelteKit dev server
pnpm dev:backend     # FastAPI dev server
pnpm dev:demo-processing  # Demo processing service
```

### 3. Verify Setup

```bash
# Check service health
pnpm health

# Run tests to verify everything works
pnpm test
```

**Service URLs:**
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Redis**: localhost:6379

## 🔧 Manual Setup (Detailed)

If automated setup fails or you prefer manual configuration:

### Frontend Setup

```bash
cd frontend

# Install dependencies
pnpm install

# Copy environment file
cp .env.example .env

# Edit environment variables
nano .env

# Start development server
pnpm dev
```

**Frontend Environment (.env):**
```bash
PUBLIC_API_BASE_URL=http://localhost:8000
PUBLIC_ENVIRONMENT=development
STEAM_API_KEY=your_steam_api_key_here
```

### Backend Setup

```bash
cd backend

# Create virtual environment
uv venv

# Activate virtual environment
source .venv/bin/activate  # Linux/macOS
# .venv\Scripts\activate.bat  # Windows

# Install dependencies
uv pip install -e .
uv pip install -e ./demo_processing

# Copy environment file
cp .env.example .env

# Edit environment variables
nano .env

# Initialize database
uv run python -c "from backend.database import init_db; init_db()"

# Start development server
uv run uvicorn backend.main:app --host 0.0.0.0 --port 8000 --reload
```

**Backend Environment (.env):**
```bash
ENV=development
DATABASE_URL=sqlite:///./database.db
REDIS_URL=redis://localhost:6379
STEAM_API_KEY=your_steam_api_key_here
JWT_SECRET=your-jwt-secret-here
CORS_ORIGINS=["http://localhost:3000"]
DEMO_STORAGE_PATH=./storage/demos
LOG_LEVEL=DEBUG
```

### Redis Setup

```bash
# Using Docker (recommended)
docker run -d -p 6379:6379 --name redis redis:7-alpine

# Or install locally
# Ubuntu/Debian:
sudo apt install redis-server
sudo systemctl start redis-server

# macOS:
brew install redis
brew services start redis
```

## 🧪 Development Workflows

### Code Development Workflow

```bash
# 1. Create feature branch
git checkout -b feature/new-feature

# 2. Make changes and test
pnpm dev
# Edit code...
pnpm test
pnpm lint

# 3. Run type checking
pnpm check

# 4. Commit changes
git add .
git commit -m "Add new feature"

# 5. Push and create PR
git push origin feature/new-feature
```

### Testing Workflow

```bash
# Run all tests
pnpm test

# Run tests with coverage
pnpm test:coverage

# Run specific test suites
pnpm test:frontend     # Vitest unit tests
pnpm test:backend      # Pytest tests
pnpm test:e2e          # Playwright E2E tests

# Run tests in watch mode
cd frontend && pnpm test:watch
cd backend && uv run pytest --watch
```

### Code Quality Workflow

```bash
# Run linting
pnpm lint

# Auto-fix linting issues
pnpm lint:fix

# Run formatting
pnpm format

# Type checking
pnpm check
```

### Database Development

```bash
# Backend database operations
cd backend

# Reset database (development only)
rm database.db
uv run python -c "from backend.database import init_db; init_db()"

# Run migrations (if applicable)
uv run alembic upgrade head

# Create new migration
uv run alembic revision --autogenerate -m "Add new table"

# Database shell
uv run python -c "from backend.database import get_connection; print(get_connection())"
```

### Demo Processing Development

```bash
# Start demo processing service
cd backend
uv run python -m demo_processing.main

# Test demo processing
uv run python -c "
from demo_processing.service import DemoProcessingService
service = DemoProcessingService()
# Test processing logic
"

# Monitor processing queue
redis-cli monitor
```

## 🛠️ IDE Setup

### VS Code Configuration

**Recommended Extensions:**
```json
{
  "recommendations": [
    "svelte.svelte-vscode",
    "ms-python.python",
    "ms-python.mypy-type-checker",
    "charliermarsh.ruff",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "redhat.vscode-yaml",
    "ms-vscode-remote.remote-containers"
  ]
}
```

**Workspace Settings (.vscode/settings.json):**
```json
{
  "python.defaultInterpreterPath": "./backend/.venv/bin/python",
  "python.linting.enabled": true,
  "python.linting.ruffEnabled": true,
  "python.formatting.provider": "black",
  "typescript.preferences.importModuleSpecifier": "relative",
  "svelte.enable-ts-plugin": true,
  "tailwindCSS.includeLanguages": {
    "svelte": "html"
  },
  "files.associations": {
    "*.svelte": "svelte"
  },
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.fixAll.ruff": true
  }
}
```

**Debug Configuration (.vscode/launch.json):**
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Backend API",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/backend/.venv/bin/uvicorn",
      "args": ["backend.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"],
      "console": "integratedTerminal",
      "cwd": "${workspaceFolder}/backend",
      "env": {
        "PYTHONPATH": "${workspaceFolder}/backend/src"
      }
    },
    {
      "name": "Demo Processing",
      "type": "python",
      "request": "launch",
      "module": "demo_processing.main",
      "console": "integratedTerminal",
      "cwd": "${workspaceFolder}/backend"
    }
  ]
}
```

### PyCharm Configuration

**Python Interpreter:**
- Add interpreter: `backend/.venv/bin/python`
- Mark `backend/src` as Sources Root
- Mark `backend/tests` as Test Sources Root

**Run Configurations:**
1. **FastAPI Server**:
   - Module: `uvicorn`
   - Parameters: `backend.main:app --host 0.0.0.0 --port 8000 --reload`
   - Working directory: `backend/`

2. **Demo Processing**:
   - Module: `demo_processing.main`
   - Working directory: `backend/`

## 🔍 Debugging Setup

### Backend Debugging

```python
# Add debugging breakpoints in code
import pdb; pdb.set_trace()

# Or use modern debugger
import ipdb; ipdb.set_trace()

# For async code
import aioipdb; await aioipdb.set_trace()
```

### Frontend Debugging

```javascript
// Browser debugging
console.log('Debug info:', data);
debugger; // Breakpoint in browser

// Svelte debugging
import { dev } from '$app/environment';
if (dev) console.log('Debug:', $state);
```

### Redis Debugging

```bash
# Monitor Redis commands
redis-cli monitor

# Check Redis data
redis-cli
> KEYS *
> GET key_name
> HGETALL hash_key
```

### Database Debugging

```bash
# SQLite CLI
sqlite3 backend/database.db
.tables
.schema users
SELECT * FROM users LIMIT 5;

# Or use a GUI tool like DB Browser for SQLite
```

## 🐳 Docker Development

### Using Docker for Development

```bash
# Start full Docker environment
pnpm dev:full

# Or start services individually
docker-compose -f docker-compose.dev.yml up frontend
docker-compose -f docker-compose.dev.yml up backend
docker-compose -f docker-compose.dev.yml up demo-processing
docker-compose -f docker-compose.dev.yml up redis
```

### Docker Development Benefits

- **Consistent Environment**: Same environment across machines
- **Service Isolation**: Services run in isolated containers
- **Hot Reloading**: Code changes reflected immediately
- **Easy Cleanup**: Simple container management

### Docker Development Workflow

```bash
# Build development images
docker-compose -f docker-compose.dev.yml build

# Start with logs
docker-compose -f docker-compose.dev.yml up

# Start in background
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop services
docker-compose -f docker-compose.dev.yml down

# Clean up volumes
docker-compose -f docker-compose.dev.yml down -v
```

## 🔄 Hot Reloading

### Frontend Hot Reloading

- **Automatic**: Vite provides instant hot module replacement
- **Preserves State**: Component state maintained during updates
- **Fast Refresh**: Quick updates without full page reload

### Backend Hot Reloading

- **Uvicorn**: `--reload` flag enables auto-restart on file changes
- **FastAPI**: Automatic OpenAPI schema updates
- **Environment Variables**: Changes require manual restart

### Demo Processing Hot Reloading

- **Development**: Manual restart required for changes
- **File Watching**: Can be implemented with tools like `watchdog`

## 📊 Development Monitoring

### Application Logs

```bash
# View all service logs
pnpm logs

# View specific service logs
pnpm logs frontend
pnpm logs backend
pnpm logs demo-processing

# Follow logs in real-time
pnpm logs -f
```

### Performance Monitoring

```bash
# Check application health
pnpm health

# Monitor resource usage
docker stats

# Check service status
pnpm status
```

### Development Metrics

```bash
# Frontend bundle analysis
cd frontend && pnpm analyze

# Backend API performance
# Check /docs endpoint for API response times

# Database query performance
# Enable SQL logging in development
```

## 🧪 Testing in Development

### Unit Testing

```bash
# Frontend unit tests
cd frontend && pnpm test

# Backend unit tests
cd backend && uv run pytest

# Watch mode for continuous testing
cd frontend && pnpm test:watch
cd backend && uv run pytest --watch
```

### Integration Testing

```bash
# API integration tests
cd backend && uv run pytest tests/test_integration_api.py

# E2E tests
cd frontend && pnpm test:e2e

# Full integration test
pnpm test:integration
```

### Test Data Management

```bash
# Reset test databases
rm backend/test_db.db frontend/test.db

# Load test fixtures
cd backend && uv run pytest --fixtures

# Generate test data
cd backend && uv run python scripts/generate_test_data.py
```

## 🔧 Configuration Management

### Environment Variables

Use environment files for different scenarios:

```bash
# Development
.env.development

# Testing
.env.test

# Local overrides
.env.local
```

### Configuration Validation

```bash
# Validate backend configuration
cd backend && uv run python -c "from backend.config import settings; print(settings)"

# Validate frontend configuration
cd frontend && pnpm check-config
```

### Secret Management

```bash
# Development secrets (not committed)
echo "SECRET_KEY=dev-secret" >> .env.local

# Production secrets
# Use proper secret management in production
```

## 📚 Development Resources

### Documentation

- **API Docs**: http://localhost:8000/docs (when running)
- **Component Storybook**: `pnpm storybook` (if configured)
- **Type Definitions**: Generated automatically

### Development Tools

- **FastAPI CLI**: `uv run brainless-cli --help`
- **Database Tools**: SQLite Browser, pgAdmin
- **Redis Tools**: Redis CLI, Redis Commander
- **API Testing**: Postman, Insomnia, curl

### Code Generation

```bash
# Generate API types for frontend
cd frontend && pnpm schema:generate

# Generate database migrations
cd backend && uv run alembic revision --autogenerate

# Generate component templates
cd frontend && pnpm generate:component ComponentName
```

## 🚨 Common Development Issues

### Port Conflicts

```bash
# Check what's using ports
lsof -i :3000 :8000 :6379

# Kill processes
kill -9 <PID>

# Change ports in environment files
```

### Database Issues

```bash
# Reset database
rm backend/database.db
pnpm dev

# Check database connections
cd backend && uv run python -c "from backend.database import get_connection; print('OK')"
```

### Python Environment Issues

```bash
# Recreate virtual environment
cd backend
rm -rf .venv
uv venv
source .venv/bin/activate
uv pip install -e .
```

### Node.js Issues

```bash
# Clear pnpm cache
pnpm store prune

# Delete node_modules and reinstall
rm -rf node_modules frontend/node_modules
pnpm install
```

### Docker Issues

```bash
# Clean Docker system
docker system prune -f

# Rebuild containers
docker-compose -f docker-compose.dev.yml build --no-cache

# Reset volumes
docker-compose -f docker-compose.dev.yml down -v
```

## 📈 Performance Optimization

### Development Performance

- **Hot Reloading**: Minimize files watched
- **Build Optimization**: Use faster build tools
- **Database**: Use WAL mode for SQLite
- **Docker**: Optimize Dockerfile layers

### Code Optimization

```bash
# Frontend bundle analysis
cd frontend && pnpm analyze

# Backend profiling
cd backend && uv run python -m cProfile -s tottime main.py

# Database query optimization
# Enable SQL query logging
```

## 🔗 Next Steps

- **[Code Style & Standards](code-style.md)** - Coding conventions
- **[Testing Strategy](testing.md)** - Testing approaches
- **[Debugging Guide](debugging.md)** - Debugging techniques
- **[Contributing](contributing.md)** - How to contribute

---

**You're all set for productive development! Happy coding! 🚀**